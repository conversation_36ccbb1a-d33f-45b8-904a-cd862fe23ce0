# 向量搜索功能说明

## 概述

向量搜索功能是图片搜索系统的核心组件之一，提供基于特征向量的语义相似度搜索能力。通过将查询文本和图片内容转换为高维向量表示，系统能够理解语义含义并返回最相关的图片结果。

## 功能特性

### 1. 语义搜索
- **全库搜索**：在整个图片库中进行语义相似度搜索
- **候选集搜索**：在指定的候选图片集合中进行精确匹配
- **多向量类型支持**：支持CLIP、ResNet、VGG等多种特征提取模型
- **相似度计算**：支持余弦相似度、欧几里得距离等多种计算方法

### 2. 性能优化
- **向量索引**：使用优化的向量检索算法
- **批量计算**：支持批量向量相似度计算
- **缓存机制**：查询向量和结果缓存
- **阈值过滤**：可配置的相似度阈值过滤

### 3. 数据管理
- **多版本支持**：支持不同版本的特征提取模型
- **质量评分**：向量质量评估和管理
- **增量更新**：支持向量数据的增量更新

## 架构设计

### 数据层
```
image_vector 表
├── id (主键)
├── image_id (图片ID)
├── vector_type (向量类型)
├── vector_dimension (向量维度)
├── feature_vector (特征向量JSON)
├── vector_norm (向量范数)
├── extraction_model (提取模型)
├── extraction_version (模型版本)
├── quality_score (质量评分)
└── created_time/updated_time
```

### 服务层
```
VectorSearchService
├── semanticSearch() - 全库语义搜索
└── semanticSearchWithinCandidates() - 候选集搜索

VectorRepository
├── 向量数据的CRUD操作
├── 批量查询和更新
└── 向量类型管理
```

## 使用方法

### 1. 基本搜索
```java
@Autowired
private VectorSearchService vectorSearchService;

// 全库语义搜索
List<ImageEntity> results = vectorSearchService.semanticSearch("可爱的小猫", 20);

// 处理搜索结果
for (ImageEntity image : results) {
    System.out.println("图片ID: " + image.getId());
    System.out.println("相似度: " + image.getSemanticScore());
    System.out.println("URL: " + image.getUrl());
}
```

### 2. 候选集搜索
```java
// 先通过元数据筛选获得候选集
Set<String> candidates = metadataSearchService.filterByMetadata(filter);

// 在候选集中进行语义搜索
List<ImageEntity> results = vectorSearchService.semanticSearchWithinCandidates(
    "现代建筑", candidates, 10);
```

### 3. 向量数据管理
```java
@Autowired
private VectorRepository vectorRepository;

// 保存向量数据
VectorRepository.VectorEntity vector = new VectorRepository.VectorEntity();
vector.setImageId("image-001");
vector.setVectorType("CLIP");
vector.setFeatureVector(featureVector);
vector.setExtractionModel("clip-vit-base-patch32");
vectorRepository.save(vector);

// 查询向量数据
Optional<VectorRepository.VectorEntity> result = 
    vectorRepository.findByImageIdAndType("image-001", "CLIP");
```

## 配置说明

### application.yml 配置
```yaml
image-search:
  vector:
    default-vector-type: CLIP
    min-similarity-threshold: 0.1
    max-search-results: 1000
    vector-dimension: 512
    
    embedding:
      enabled: true
      model-type: CLIP
      model-name: clip-vit-base-patch32
      api-endpoint: "http://localhost:8080/api/embeddings"
      timeout-ms: 10000
      
    similarity:
      method: cosine
      normalize-vectors: true
      use-precomputed-norm: true
      
    cache:
      enable-query-cache: true
      query-cache-expire-seconds: 3600
```

### 配置参数说明
- `default-vector-type`: 默认使用的向量类型
- `min-similarity-threshold`: 最小相似度阈值，低于此值的结果被过滤
- `vector-dimension`: 向量维度，需要与模型输出维度一致
- `similarity.method`: 相似度计算方法（cosine/euclidean/dot）
- `cache.enable-query-cache`: 是否启用查询向量缓存

## 部署指南

### 1. 数据库初始化
```sql
-- 执行数据库表创建脚本
source src/main/resources/sql/schema.sql;

-- 如果有现有数据需要迁移
source src/main/resources/sql/migration.sql;
```

### 2. 嵌入模型集成
当前实现使用模拟的向量生成，生产环境需要集成真实的嵌入模型：

```java
// 替换 VectorSearchServiceImpl 中的 convertTextToVector 方法
private float[] convertTextToVector(String text) {
    // 调用实际的嵌入模型API
    // 例如：OpenAI Embeddings、Sentence Transformers等
    return embeddingClient.getEmbedding(text);
}
```

### 3. 性能优化建议
- **向量索引**：考虑使用Faiss、Annoy等向量索引库
- **GPU加速**：使用GPU进行向量计算加速
- **分布式部署**：大规模数据可考虑分布式向量搜索
- **缓存策略**：合理配置缓存参数以平衡性能和内存使用

## 测试

### 单元测试
```bash
# 运行向量搜索相关测试
mvn test -Dtest=VectorSearchServiceImplTest
```

### 集成测试
```bash
# 运行完整的集成测试
mvn test -Dtest=*IntegrationTest
```

## 监控和调优

### 关键指标
- 搜索响应时间
- 向量计算耗时
- 缓存命中率
- 相似度分布

### 日志配置
```yaml
logging:
  level:
    cn.iflytek.imagesearch.domain.service.search.impl.VectorSearchServiceImpl: DEBUG
    cn.iflytek.imagesearch.infrastructure.repository.VectorRepositoryImpl: DEBUG
```

## 常见问题

### Q: 如何提高搜索精度？
A: 
1. 调整相似度阈值
2. 使用更好的特征提取模型
3. 增加向量维度
4. 优化查询文本预处理

### Q: 如何处理大规模向量数据？
A: 
1. 使用专业的向量数据库（如Milvus、Pinecone）
2. 实施向量索引和分片策略
3. 考虑近似最近邻搜索算法

### Q: 如何集成不同的嵌入模型？
A: 
1. 实现统一的嵌入服务接口
2. 配置不同的向量类型
3. 支持模型版本管理和切换
