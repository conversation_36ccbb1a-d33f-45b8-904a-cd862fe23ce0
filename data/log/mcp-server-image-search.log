2025-08-07T10:00:52.399+08:00  INFO 21852 --- [mcp-server-image-search] [main] c.i.imagesearch.MinIOConnectionTest      : Starting MinIOConnectionTest using Java 17.0.12 with PID 21852 (started by aofu in C:\Users\<USER>\Downloads\image-search)
2025-08-07T10:00:52.403+08:00  INFO 21852 --- [mcp-server-image-search] [main] c.i.imagesearch.MinIOConnectionTest      : No active profile set, falling back to 1 default profile: "default"
2025-08-07T10:00:53.179+08:00  WARN 21852 --- [mcp-server-image-search] [main] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[cn.iflytek.imagesearch]' package. Please check your configuration.
2025-08-07T10:00:53.957+08:00  INFO 21852 --- [mcp-server-image-search] [main] c.i.i.types.config.MinIOConfig           : MinIO客户端初始化成功，端点: http://************:9000, 存储桶: image-storage
2025-08-07T10:00:55.488+08:00  INFO 21852 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable tools capabilities, notification: true
2025-08-07T10:00:55.674+08:00  INFO 21852 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Registered tools: 3
2025-08-07T10:00:55.674+08:00  INFO 21852 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable resources capabilities, notification: true
2025-08-07T10:00:55.675+08:00  INFO 21852 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable prompts capabilities, notification: true
2025-08-07T10:00:55.676+08:00  INFO 21852 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable completions capabilities
2025-08-07T10:00:55.849+08:00  INFO 21852 --- [mcp-server-image-search] [main] c.i.imagesearch.MinIOConnectionTest      : Started MinIOConnectionTest in 3.869 seconds (process running for 5.779)
2025-08-07T10:00:57.036+08:00  INFO 21852 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : 图片上传成功: images/c2f41679-2042-4f36-82aa-6a8849d50b4a.png
2025-08-07T10:00:57.186+08:00  INFO 21852 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : 删除图片成功: images/c2f41679-2042-4f36-82aa-6a8849d50b4a.png
2025-08-07T10:00:57.259+08:00  INFO 21852 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : 图片上传成功: images/2cfec249-7c29-4225-bbdc-35970bf408fa.png
2025-08-07T10:00:57.492+08:00  INFO 21852 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : 删除图片成功: images/2cfec249-7c29-4225-bbdc-35970bf408fa.png
2025-08-07T10:00:57.537+08:00  INFO 21852 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : 图片上传成功: images/0436a17c-7b89-4c0c-8326-37a489b70c51.jpg
2025-08-07T10:00:57.636+08:00  INFO 21852 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : 删除图片成功: images/0436a17c-7b89-4c0c-8326-37a489b70c51.jpg
2025-08-07T10:00:57.680+08:00  INFO 21852 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : 缩略图上传成功: thumbnails/7362d0e8-327f-4c75-bd3f-308fe57c8c9f.png
2025-08-07T10:00:57.807+08:00  INFO 21852 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : 删除图片成功: thumbnails/7362d0e8-327f-4c75-bd3f-308fe57c8c9f.png
2025-08-07T10:26:16.552+08:00  INFO 15072 --- [mcp-server-image-search] [main] c.i.i.ImageSearchApplicationTests        : Starting ImageSearchApplicationTests using Java 17.0.12 with PID 15072 (started by aofu in C:\Users\<USER>\Downloads\image-search)
2025-08-07T10:26:16.555+08:00  INFO 15072 --- [mcp-server-image-search] [main] c.i.i.ImageSearchApplicationTests        : No active profile set, falling back to 1 default profile: "default"
2025-08-07T10:26:17.633+08:00  WARN 15072 --- [mcp-server-image-search] [main] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[cn.iflytek.imagesearch]' package. Please check your configuration.
2025-08-07T10:26:18.627+08:00  INFO 15072 --- [mcp-server-image-search] [main] c.i.i.types.config.MinIOConfig           : MinIO�ͻ��˳�ʼ���ɹ����˵�: http://localhost:9000, �洢Ͱ: image-storage
2025-08-07T10:26:18.860+08:00 ERROR 15072 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ��ʼ��MinIO�洢Ͱʧ��

java.net.ConnectException: Failed to connect to localhost/[0:0:0:0:0:0:0:1]:9000
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517) ~[okhttp-4.12.0.jar:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:842) ~[na:na]
	Suppressed: java.net.ConnectException: Failed to connect to localhost/127.0.0.1:9000
		... 19 common frames omitted
	Caused by: java.net.ConnectException: Connection refused: no further information
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
		at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
		... 18 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method) ~[na:na]
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[na:na]
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[na:na]
	at java.base/java.net.Socket.connect(Socket.java:633) ~[na:na]
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295) ~[okhttp-4.12.0.jar:na]
	... 18 common frames omitted

2025-08-07T10:26:20.338+08:00  INFO 15072 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable tools capabilities, notification: true
2025-08-07T10:26:20.512+08:00  INFO 15072 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Registered tools: 3
2025-08-07T10:26:20.513+08:00  INFO 15072 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable resources capabilities, notification: true
2025-08-07T10:26:20.515+08:00  INFO 15072 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable prompts capabilities, notification: true
2025-08-07T10:26:20.519+08:00  INFO 15072 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable completions capabilities
2025-08-07T10:26:20.723+08:00  INFO 15072 --- [mcp-server-image-search] [main] c.i.i.ImageSearchApplicationTests        : Started ImageSearchApplicationTests in 4.755 seconds (process running for 5.898)
2025-08-07T10:26:21.810+08:00  INFO 15072 --- [mcp-server-image-search] [main] t.c.s.AnnotationConfigContextLoaderUtils : Could not detect default configuration classes for test class [cn.iflytek.imagesearch.MinIOConnectionTest]: MinIOConnectionTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
2025-08-07T10:26:21.817+08:00  INFO 15072 --- [mcp-server-image-search] [main] .b.t.c.SpringBootTestContextBootstrapper : Found @SpringBootConfiguration cn.iflytek.imagesearch.ImageSearchApplication for test class cn.iflytek.imagesearch.MinIOConnectionTest
2025-08-07T10:26:21.868+08:00  INFO 15072 --- [mcp-server-image-search] [main] c.i.imagesearch.MinIOConnectionTest      : Starting MinIOConnectionTest using Java 17.0.12 with PID 15072 (started by aofu in C:\Users\<USER>\Downloads\image-search)
2025-08-07T10:26:21.869+08:00  INFO 15072 --- [mcp-server-image-search] [main] c.i.imagesearch.MinIOConnectionTest      : No active profile set, falling back to 1 default profile: "default"
2025-08-07T10:26:22.105+08:00  WARN 15072 --- [mcp-server-image-search] [main] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[cn.iflytek.imagesearch]' package. Please check your configuration.
2025-08-07T10:26:22.134+08:00  INFO 15072 --- [mcp-server-image-search] [main] c.i.i.types.config.MinIOConfig           : MinIO�ͻ��˳�ʼ���ɹ����˵�: http://************:9000, �洢Ͱ: image-storage
2025-08-07T10:26:22.651+08:00  INFO 15072 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable tools capabilities, notification: true
2025-08-07T10:26:22.679+08:00  INFO 15072 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Registered tools: 3
2025-08-07T10:26:22.680+08:00  INFO 15072 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable resources capabilities, notification: true
2025-08-07T10:26:22.681+08:00  INFO 15072 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable prompts capabilities, notification: true
2025-08-07T10:26:22.682+08:00  INFO 15072 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable completions capabilities
2025-08-07T10:26:22.737+08:00  INFO 15072 --- [mcp-server-image-search] [main] c.i.imagesearch.MinIOConnectionTest      : Started MinIOConnectionTest in 0.912 seconds (process running for 7.912)
2025-08-07T10:26:22.815+08:00  INFO 15072 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ͼƬ�ϴ��ɹ�: images/57c50638-2fcc-4ad2-af49-f44d63aa4f20.png
2025-08-07T10:26:22.971+08:00  INFO 15072 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ɾ��ͼƬ�ɹ�: images/57c50638-2fcc-4ad2-af49-f44d63aa4f20.png
2025-08-07T10:26:23.015+08:00  INFO 15072 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ͼƬ�ϴ��ɹ�: images/e6922333-660e-4757-a74f-d0e7198a4102.png
2025-08-07T10:26:23.182+08:00  INFO 15072 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ɾ��ͼƬ�ɹ�: images/e6922333-660e-4757-a74f-d0e7198a4102.png
2025-08-07T10:26:23.227+08:00  INFO 15072 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ͼƬ�ϴ��ɹ�: images/21e96c11-d699-46c0-b82f-9c8ed5b5c55d.jpg
2025-08-07T10:26:23.328+08:00  INFO 15072 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ɾ��ͼƬ�ɹ�: images/21e96c11-d699-46c0-b82f-9c8ed5b5c55d.jpg
2025-08-07T10:26:23.366+08:00  INFO 15072 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ����ͼ�ϴ��ɹ�: thumbnails/a1a9ee75-6274-4cc6-a9cb-84eace04e413.png
2025-08-07T10:26:23.485+08:00  INFO 15072 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ɾ��ͼƬ�ɹ�: thumbnails/a1a9ee75-6274-4cc6-a9cb-84eace04e413.png
2025-08-07T10:26:53.934+08:00  INFO 18520 --- [mcp-server-image-search] [main] c.i.i.ImageSearchApplicationTests        : Starting ImageSearchApplicationTests using Java 17.0.12 with PID 18520 (started by aofu in C:\Users\<USER>\Downloads\image-search)
2025-08-07T10:26:53.937+08:00  INFO 18520 --- [mcp-server-image-search] [main] c.i.i.ImageSearchApplicationTests        : No active profile set, falling back to 1 default profile: "default"
2025-08-07T10:26:54.736+08:00  WARN 18520 --- [mcp-server-image-search] [main] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[cn.iflytek.imagesearch]' package. Please check your configuration.
2025-08-07T10:26:55.403+08:00  INFO 18520 --- [mcp-server-image-search] [main] c.i.i.types.config.MinIOConfig           : MinIO�ͻ��˳�ʼ���ɹ����˵�: http://localhost:9000, �洢Ͱ: image-storage
2025-08-07T10:26:55.529+08:00 ERROR 18520 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ��ʼ��MinIO�洢Ͱʧ��

java.net.ConnectException: Failed to connect to localhost/[0:0:0:0:0:0:0:1]:9000
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517) ~[okhttp-4.12.0.jar:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:842) ~[na:na]
	Suppressed: java.net.ConnectException: Failed to connect to localhost/127.0.0.1:9000
		... 19 common frames omitted
	Caused by: java.net.ConnectException: Connection refused: no further information
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
		at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
		... 18 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method) ~[na:na]
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[na:na]
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[na:na]
	at java.base/java.net.Socket.connect(Socket.java:633) ~[na:na]
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295) ~[okhttp-4.12.0.jar:na]
	... 18 common frames omitted

2025-08-07T10:26:56.808+08:00  INFO 18520 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable tools capabilities, notification: true
2025-08-07T10:26:57.028+08:00  INFO 18520 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Registered tools: 3
2025-08-07T10:26:57.029+08:00  INFO 18520 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable resources capabilities, notification: true
2025-08-07T10:26:57.032+08:00  INFO 18520 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable prompts capabilities, notification: true
2025-08-07T10:26:57.036+08:00  INFO 18520 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable completions capabilities
2025-08-07T10:26:57.394+08:00  INFO 18520 --- [mcp-server-image-search] [main] c.i.i.ImageSearchApplicationTests        : Started ImageSearchApplicationTests in 3.818 seconds (process running for 5.004)
2025-08-07T10:26:58.592+08:00  INFO 18520 --- [mcp-server-image-search] [main] t.c.s.AnnotationConfigContextLoaderUtils : Could not detect default configuration classes for test class [cn.iflytek.imagesearch.MinIOConnectionTest]: MinIOConnectionTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
2025-08-07T10:26:58.601+08:00  INFO 18520 --- [mcp-server-image-search] [main] .b.t.c.SpringBootTestContextBootstrapper : Found @SpringBootConfiguration cn.iflytek.imagesearch.ImageSearchApplication for test class cn.iflytek.imagesearch.MinIOConnectionTest
2025-08-07T10:26:58.665+08:00  INFO 18520 --- [mcp-server-image-search] [main] c.i.imagesearch.MinIOConnectionTest      : Starting MinIOConnectionTest using Java 17.0.12 with PID 18520 (started by aofu in C:\Users\<USER>\Downloads\image-search)
2025-08-07T10:26:58.665+08:00  INFO 18520 --- [mcp-server-image-search] [main] c.i.imagesearch.MinIOConnectionTest      : No active profile set, falling back to 1 default profile: "default"
2025-08-07T10:26:58.954+08:00  WARN 18520 --- [mcp-server-image-search] [main] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[cn.iflytek.imagesearch]' package. Please check your configuration.
2025-08-07T10:26:58.993+08:00  INFO 18520 --- [mcp-server-image-search] [main] c.i.i.types.config.MinIOConfig           : MinIO�ͻ��˳�ʼ���ɹ����˵�: http://************:9000, �洢Ͱ: image-storage
2025-08-07T10:26:59.634+08:00  INFO 18520 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable tools capabilities, notification: true
2025-08-07T10:26:59.670+08:00  INFO 18520 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Registered tools: 3
2025-08-07T10:26:59.671+08:00  INFO 18520 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable resources capabilities, notification: true
2025-08-07T10:26:59.671+08:00  INFO 18520 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable prompts capabilities, notification: true
2025-08-07T10:26:59.672+08:00  INFO 18520 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable completions capabilities
2025-08-07T10:26:59.715+08:00  INFO 18520 --- [mcp-server-image-search] [main] c.i.imagesearch.MinIOConnectionTest      : Started MinIOConnectionTest in 1.101 seconds (process running for 7.325)
2025-08-07T10:26:59.781+08:00  INFO 18520 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ͼƬ�ϴ��ɹ�: images/1d5af60b-e559-4256-a321-46fdb05baa39.png
2025-08-07T10:26:59.944+08:00  INFO 18520 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ɾ��ͼƬ�ɹ�: images/1d5af60b-e559-4256-a321-46fdb05baa39.png
2025-08-07T10:26:59.989+08:00  INFO 18520 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ͼƬ�ϴ��ɹ�: images/959368bd-cab3-4045-ac78-85d9491b71ad.png
2025-08-07T10:27:00.181+08:00  INFO 18520 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ɾ��ͼƬ�ɹ�: images/959368bd-cab3-4045-ac78-85d9491b71ad.png
2025-08-07T10:27:00.226+08:00  INFO 18520 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ͼƬ�ϴ��ɹ�: images/03b36a4b-48eb-4451-8a5b-b65fa61362af.jpg
2025-08-07T10:27:00.322+08:00  INFO 18520 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ɾ��ͼƬ�ɹ�: images/03b36a4b-48eb-4451-8a5b-b65fa61362af.jpg
2025-08-07T10:27:00.363+08:00  INFO 18520 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ����ͼ�ϴ��ɹ�: thumbnails/f010ba2e-f757-4388-bc54-b7bbba0fce4b.png
2025-08-07T10:27:00.480+08:00  INFO 18520 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ɾ��ͼƬ�ɹ�: thumbnails/f010ba2e-f757-4388-bc54-b7bbba0fce4b.png
2025-08-07T10:27:07.431+08:00  INFO 12284 --- [mcp-server-image-search] [main] c.i.i.ImageSearchApplicationTests        : Starting ImageSearchApplicationTests using Java 17.0.12 with PID 12284 (started by aofu in C:\Users\<USER>\Downloads\image-search)
2025-08-07T10:27:07.434+08:00  INFO 12284 --- [mcp-server-image-search] [main] c.i.i.ImageSearchApplicationTests        : No active profile set, falling back to 1 default profile: "default"
2025-08-07T10:27:08.359+08:00  WARN 12284 --- [mcp-server-image-search] [main] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[cn.iflytek.imagesearch]' package. Please check your configuration.
2025-08-07T10:27:09.042+08:00  INFO 12284 --- [mcp-server-image-search] [main] c.i.i.types.config.MinIOConfig           : MinIO�ͻ��˳�ʼ���ɹ����˵�: http://localhost:9000, �洢Ͱ: image-storage
2025-08-07T10:27:09.161+08:00 ERROR 12284 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ��ʼ��MinIO�洢Ͱʧ��

java.net.ConnectException: Failed to connect to localhost/[0:0:0:0:0:0:0:1]:9000
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517) ~[okhttp-4.12.0.jar:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:842) ~[na:na]
	Suppressed: java.net.ConnectException: Failed to connect to localhost/127.0.0.1:9000
		... 19 common frames omitted
	Caused by: java.net.ConnectException: Connection refused: no further information
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
		at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
		... 18 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method) ~[na:na]
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[na:na]
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[na:na]
	at java.base/java.net.Socket.connect(Socket.java:633) ~[na:na]
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295) ~[okhttp-4.12.0.jar:na]
	... 18 common frames omitted

2025-08-07T10:27:10.457+08:00  INFO 12284 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable tools capabilities, notification: true
2025-08-07T10:27:10.649+08:00  INFO 12284 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Registered tools: 3
2025-08-07T10:27:10.650+08:00  INFO 12284 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable resources capabilities, notification: true
2025-08-07T10:27:10.652+08:00  INFO 12284 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable prompts capabilities, notification: true
2025-08-07T10:27:10.656+08:00  INFO 12284 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable completions capabilities
2025-08-07T10:27:10.864+08:00  INFO 12284 --- [mcp-server-image-search] [main] c.i.i.ImageSearchApplicationTests        : Started ImageSearchApplicationTests in 3.805 seconds (process running for 4.905)
2025-08-07T10:27:11.986+08:00  INFO 12284 --- [mcp-server-image-search] [main] t.c.s.AnnotationConfigContextLoaderUtils : Could not detect default configuration classes for test class [cn.iflytek.imagesearch.MinIOConnectionTest]: MinIOConnectionTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
2025-08-07T10:27:11.997+08:00  INFO 12284 --- [mcp-server-image-search] [main] .b.t.c.SpringBootTestContextBootstrapper : Found @SpringBootConfiguration cn.iflytek.imagesearch.ImageSearchApplication for test class cn.iflytek.imagesearch.MinIOConnectionTest
2025-08-07T10:27:12.071+08:00  INFO 12284 --- [mcp-server-image-search] [main] c.i.imagesearch.MinIOConnectionTest      : Starting MinIOConnectionTest using Java 17.0.12 with PID 12284 (started by aofu in C:\Users\<USER>\Downloads\image-search)
2025-08-07T10:27:12.072+08:00  INFO 12284 --- [mcp-server-image-search] [main] c.i.imagesearch.MinIOConnectionTest      : No active profile set, falling back to 1 default profile: "default"
2025-08-07T10:27:12.269+08:00  WARN 12284 --- [mcp-server-image-search] [main] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[cn.iflytek.imagesearch]' package. Please check your configuration.
2025-08-07T10:27:12.295+08:00  INFO 12284 --- [mcp-server-image-search] [main] c.i.i.types.config.MinIOConfig           : MinIO�ͻ��˳�ʼ���ɹ����˵�: http://************:9000, �洢Ͱ: image-storage
2025-08-07T10:27:12.876+08:00  INFO 12284 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable tools capabilities, notification: true
2025-08-07T10:27:12.896+08:00  INFO 12284 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Registered tools: 3
2025-08-07T10:27:12.897+08:00  INFO 12284 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable resources capabilities, notification: true
2025-08-07T10:27:12.897+08:00  INFO 12284 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable prompts capabilities, notification: true
2025-08-07T10:27:12.898+08:00  INFO 12284 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable completions capabilities
2025-08-07T10:27:12.939+08:00  INFO 12284 --- [mcp-server-image-search] [main] c.i.imagesearch.MinIOConnectionTest      : Started MinIOConnectionTest in 0.923 seconds (process running for 6.981)
2025-08-07T10:27:13.051+08:00  INFO 12284 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ͼƬ�ϴ��ɹ�: images/104a110a-bde5-462c-b7d3-0b7179285841.png
2025-08-07T10:27:13.198+08:00  INFO 12284 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ɾ��ͼƬ�ɹ�: images/104a110a-bde5-462c-b7d3-0b7179285841.png
2025-08-07T10:27:13.245+08:00  INFO 12284 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ͼƬ�ϴ��ɹ�: images/7443db35-c76f-480b-b59d-8bc6e0e4c833.png
2025-08-07T10:27:13.435+08:00  INFO 12284 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ɾ��ͼƬ�ɹ�: images/7443db35-c76f-480b-b59d-8bc6e0e4c833.png
2025-08-07T10:27:13.486+08:00  INFO 12284 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ͼƬ�ϴ��ɹ�: images/1e4e65dc-9af9-44bc-ae44-bbffa9e747a1.jpg
2025-08-07T10:27:13.583+08:00  INFO 12284 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ɾ��ͼƬ�ɹ�: images/1e4e65dc-9af9-44bc-ae44-bbffa9e747a1.jpg
2025-08-07T10:27:13.624+08:00  INFO 12284 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ����ͼ�ϴ��ɹ�: thumbnails/5664454b-d82c-45ed-9b16-4464608d52a1.png
2025-08-07T10:27:13.753+08:00  INFO 12284 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ɾ��ͼƬ�ɹ�: thumbnails/5664454b-d82c-45ed-9b16-4464608d52a1.png
2025-08-07T10:27:20.063+08:00  INFO 28416 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : Starting ImageSearchApplication using Java 17.0.12 with PID 28416 (C:\Users\<USER>\Downloads\image-search\target\classes started by aofu in C:\Users\<USER>\Downloads\image-search)
2025-08-07T10:27:20.069+08:00  INFO 28416 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-07T10:27:20.084+08:00  INFO 27776 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : Starting ImageSearchApplication using Java 17.0.12 with PID 27776 (C:\Users\<USER>\Downloads\image-search\target\classes started by aofu in C:\Users\<USER>\Downloads\image-search)
2025-08-07T10:27:20.090+08:00  INFO 27776 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-07T10:27:21.111+08:00  INFO 23492 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : Starting ImageSearchApplication using Java 17.0.12 with PID 23492 (C:\Users\<USER>\Downloads\image-search\target\classes started by aofu in C:\Users\<USER>\Downloads\image-search)
2025-08-07T10:27:21.117+08:00  INFO 23492 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-07T10:27:21.566+08:00  WARN 27776 --- [mcp-server-image-search] [main] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[cn.iflytek.imagesearch]' package. Please check your configuration.
2025-08-07T10:27:21.568+08:00  WARN 28416 --- [mcp-server-image-search] [main] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[cn.iflytek.imagesearch]' package. Please check your configuration.
2025-08-07T10:27:22.535+08:00  WARN 23492 --- [mcp-server-image-search] [main] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[cn.iflytek.imagesearch]' package. Please check your configuration.
2025-08-07T10:27:22.605+08:00  INFO 27776 --- [mcp-server-image-search] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8101 (http)
2025-08-07T10:27:22.613+08:00  INFO 28416 --- [mcp-server-image-search] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8101 (http)
2025-08-07T10:27:22.626+08:00  INFO 27776 --- [mcp-server-image-search] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-07T10:27:22.626+08:00  INFO 27776 --- [mcp-server-image-search] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-07T10:27:22.632+08:00  INFO 28416 --- [mcp-server-image-search] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-07T10:27:22.633+08:00  INFO 28416 --- [mcp-server-image-search] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-07T10:27:22.736+08:00  INFO 27776 --- [mcp-server-image-search] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-07T10:27:22.737+08:00  INFO 27776 --- [mcp-server-image-search] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2566 ms
2025-08-07T10:27:22.746+08:00  INFO 28416 --- [mcp-server-image-search] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-07T10:27:22.747+08:00  INFO 28416 --- [mcp-server-image-search] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2603 ms
2025-08-07T10:27:23.397+08:00  INFO 27776 --- [mcp-server-image-search] [main] c.i.i.types.config.MinIOConfig           : MinIO�ͻ��˳�ʼ���ɹ����˵�: http://localhost:9000, �洢Ͱ: image-storage
2025-08-07T10:27:23.416+08:00  INFO 28416 --- [mcp-server-image-search] [main] c.i.i.types.config.MinIOConfig           : MinIO�ͻ��˳�ʼ���ɹ����˵�: http://localhost:9000, �洢Ͱ: image-storage
2025-08-07T10:27:23.517+08:00 ERROR 27776 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ��ʼ��MinIO�洢Ͱʧ��

java.net.ConnectException: Failed to connect to localhost/[0:0:0:0:0:0:0:1]:9000
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517) ~[okhttp-4.12.0.jar:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:842) ~[na:na]
	Suppressed: java.net.ConnectException: Failed to connect to localhost/127.0.0.1:9000
		... 19 common frames omitted
	Caused by: java.net.ConnectException: Connection refused: no further information
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
		at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
		... 18 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method) ~[na:na]
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[na:na]
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[na:na]
	at java.base/java.net.Socket.connect(Socket.java:633) ~[na:na]
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295) ~[okhttp-4.12.0.jar:na]
	... 18 common frames omitted

2025-08-07T10:27:23.555+08:00 ERROR 28416 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ��ʼ��MinIO�洢Ͱʧ��

java.net.ConnectException: Failed to connect to localhost/[0:0:0:0:0:0:0:1]:9000
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517) ~[okhttp-4.12.0.jar:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:842) ~[na:na]
	Suppressed: java.net.ConnectException: Failed to connect to localhost/127.0.0.1:9000
		... 19 common frames omitted
	Caused by: java.net.ConnectException: Connection refused: no further information
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
		at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
		... 18 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method) ~[na:na]
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[na:na]
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[na:na]
	at java.base/java.net.Socket.connect(Socket.java:633) ~[na:na]
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295) ~[okhttp-4.12.0.jar:na]
	... 18 common frames omitted

2025-08-07T10:27:23.780+08:00  INFO 23492 --- [mcp-server-image-search] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8101 (http)
2025-08-07T10:27:23.803+08:00  INFO 23492 --- [mcp-server-image-search] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-07T10:27:23.804+08:00  INFO 23492 --- [mcp-server-image-search] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-07T10:27:23.942+08:00  INFO 23492 --- [mcp-server-image-search] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-07T10:27:23.943+08:00  INFO 23492 --- [mcp-server-image-search] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2743 ms
2025-08-07T10:27:24.783+08:00  INFO 23492 --- [mcp-server-image-search] [main] c.i.i.types.config.MinIOConfig           : MinIO�ͻ��˳�ʼ���ɹ����˵�: http://localhost:9000, �洢Ͱ: image-storage
2025-08-07T10:27:25.014+08:00 ERROR 23492 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : ��ʼ��MinIO�洢Ͱʧ��

java.net.ConnectException: Failed to connect to localhost/[0:0:0:0:0:0:0:1]:9000
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517) ~[okhttp-4.12.0.jar:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:842) ~[na:na]
	Suppressed: java.net.ConnectException: Failed to connect to localhost/127.0.0.1:9000
		... 19 common frames omitted
	Caused by: java.net.ConnectException: Connection refused: no further information
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
		at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
		... 18 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method) ~[na:na]
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[na:na]
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[na:na]
	at java.base/java.net.Socket.connect(Socket.java:633) ~[na:na]
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295) ~[okhttp-4.12.0.jar:na]
	... 18 common frames omitted

2025-08-07T10:27:25.123+08:00  INFO 27776 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable tools capabilities, notification: true
2025-08-07T10:27:25.259+08:00  INFO 28416 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable tools capabilities, notification: true
2025-08-07T10:27:25.392+08:00  INFO 27776 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Registered tools: 3
2025-08-07T10:27:25.393+08:00  INFO 27776 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable resources capabilities, notification: true
2025-08-07T10:27:25.395+08:00  INFO 27776 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable prompts capabilities, notification: true
2025-08-07T10:27:25.398+08:00  INFO 27776 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable completions capabilities
2025-08-07T10:27:25.588+08:00  INFO 28416 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Registered tools: 3
2025-08-07T10:27:25.590+08:00  INFO 28416 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable resources capabilities, notification: true
2025-08-07T10:27:25.596+08:00  INFO 28416 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable prompts capabilities, notification: true
2025-08-07T10:27:25.600+08:00  INFO 28416 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable completions capabilities
2025-08-07T10:27:25.712+08:00  INFO 27776 --- [mcp-server-image-search] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8101 (http) with context path '/'
2025-08-07T10:27:25.736+08:00  INFO 27776 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : Started ImageSearchApplication in 6.384 seconds (process running for 6.897)
2025-08-07T10:27:25.869+08:00  WARN 28416 --- [mcp-server-image-search] [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-07T10:27:25.922+08:00  INFO 28416 --- [mcp-server-image-search] [main] .s.b.a.l.ConditionEvaluationReportLogger :

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-07T10:27:25.949+08:00 ERROR 28416 --- [mcp-server-image-search] [main] o.s.b.d.LoggingFailureAnalysisReporter   :

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8101 was already in use.

Action:

Identify and stop the process that's listening on port 8101 or configure this application to listen on another port.

2025-08-07T10:27:26.629+08:00  INFO 23492 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable tools capabilities, notification: true
2025-08-07T10:27:26.855+08:00  INFO 23492 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Registered tools: 3
2025-08-07T10:27:26.857+08:00  INFO 23492 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable resources capabilities, notification: true
2025-08-07T10:27:26.860+08:00  INFO 23492 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable prompts capabilities, notification: true
2025-08-07T10:27:26.863+08:00  INFO 23492 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable completions capabilities
2025-08-07T10:27:27.091+08:00  WARN 23492 --- [mcp-server-image-search] [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-07T10:27:27.178+08:00  INFO 23492 --- [mcp-server-image-search] [main] .s.b.a.l.ConditionEvaluationReportLogger :

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-07T10:27:27.227+08:00 ERROR 23492 --- [mcp-server-image-search] [main] o.s.b.d.LoggingFailureAnalysisReporter   :

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8101 was already in use.

Action:

Identify and stop the process that's listening on port 8101 or configure this application to listen on another port.

2025-08-07T10:28:27.250+08:00  INFO 1408 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : Starting ImageSearchApplication using Java 17.0.12 with PID 1408 (C:\Users\<USER>\Downloads\image-search\target\classes started by aofu in C:\Users\<USER>\Downloads\image-search)
2025-08-07T10:28:27.254+08:00  INFO 1408 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-07T10:28:28.203+08:00  WARN 1408 --- [mcp-server-image-search] [main] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[cn.iflytek.imagesearch]' package. Please check your configuration.
2025-08-07T10:28:28.831+08:00  INFO 1408 --- [mcp-server-image-search] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8101 (http)
2025-08-07T10:28:28.846+08:00  INFO 1408 --- [mcp-server-image-search] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-07T10:28:28.846+08:00  INFO 1408 --- [mcp-server-image-search] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-07T10:28:28.957+08:00  INFO 1408 --- [mcp-server-image-search] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-07T10:28:28.959+08:00  INFO 1408 --- [mcp-server-image-search] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1628 ms
2025-08-07T10:28:29.372+08:00  INFO 1408 --- [mcp-server-image-search] [main] c.i.i.types.config.MinIOConfig           : MinIO客户端初始化成功，端点: http://localhost:9000, 存储桶: image-storage
2025-08-07T10:28:29.466+08:00 ERROR 1408 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : 初始化MinIO存储桶失败

java.net.ConnectException: Failed to connect to localhost/[0:0:0:0:0:0:0:1]:9000
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517) ~[okhttp-4.12.0.jar:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:842) ~[na:na]
	Suppressed: java.net.ConnectException: Failed to connect to localhost/127.0.0.1:9000
		... 19 common frames omitted
	Caused by: java.net.ConnectException: Connection refused: no further information
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
		at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
		... 18 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method) ~[na:na]
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[na:na]
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[na:na]
	at java.base/java.net.Socket.connect(Socket.java:633) ~[na:na]
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295) ~[okhttp-4.12.0.jar:na]
	... 18 common frames omitted

2025-08-07T10:28:30.429+08:00  INFO 1408 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable tools capabilities, notification: true
2025-08-07T10:28:30.605+08:00  INFO 1408 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Registered tools: 3
2025-08-07T10:28:30.606+08:00  INFO 1408 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable resources capabilities, notification: true
2025-08-07T10:28:30.607+08:00  INFO 1408 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable prompts capabilities, notification: true
2025-08-07T10:28:30.609+08:00  INFO 1408 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable completions capabilities
2025-08-07T10:28:30.797+08:00  WARN 1408 --- [mcp-server-image-search] [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-07T10:28:30.872+08:00  INFO 1408 --- [mcp-server-image-search] [main] .s.b.a.l.ConditionEvaluationReportLogger :

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-07T10:28:30.927+08:00 ERROR 1408 --- [mcp-server-image-search] [main] o.s.b.d.LoggingFailureAnalysisReporter   :

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8101 was already in use.

Action:

Identify and stop the process that's listening on port 8101 or configure this application to listen on another port.

2025-08-07T10:29:25.043+08:00  INFO 16804 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : Starting ImageSearchApplication using Java 17.0.12 with PID 16804 (C:\Users\<USER>\Downloads\image-search\target\classes started by aofu in C:\Users\<USER>\Downloads\image-search)
2025-08-07T10:29:25.049+08:00  INFO 16804 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-07T10:29:26.015+08:00  WARN 16804 --- [mcp-server-image-search] [main] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[cn.iflytek.imagesearch]' package. Please check your configuration.
2025-08-07T10:29:26.650+08:00  INFO 16804 --- [mcp-server-image-search] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8099 (http)
2025-08-07T10:29:26.667+08:00  INFO 16804 --- [mcp-server-image-search] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-07T10:29:26.668+08:00  INFO 16804 --- [mcp-server-image-search] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-07T10:29:26.757+08:00  INFO 16804 --- [mcp-server-image-search] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-07T10:29:26.758+08:00  INFO 16804 --- [mcp-server-image-search] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1652 ms
2025-08-07T10:29:27.191+08:00  INFO 16804 --- [mcp-server-image-search] [main] c.i.i.types.config.MinIOConfig           : MinIO客户端初始化成功，端点: http://localhost:9000, 存储桶: image-storage
2025-08-07T10:29:27.278+08:00 ERROR 16804 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : 初始化MinIO存储桶失败

java.net.ConnectException: Failed to connect to localhost/[0:0:0:0:0:0:0:1]:9000
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517) ~[okhttp-4.12.0.jar:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:842) ~[na:na]
	Suppressed: java.net.ConnectException: Failed to connect to localhost/127.0.0.1:9000
		... 19 common frames omitted
	Caused by: java.net.ConnectException: Connection refused: no further information
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
		at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
		... 18 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method) ~[na:na]
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[na:na]
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[na:na]
	at java.base/java.net.Socket.connect(Socket.java:633) ~[na:na]
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295) ~[okhttp-4.12.0.jar:na]
	... 18 common frames omitted

2025-08-07T10:29:28.257+08:00  INFO 16804 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable tools capabilities, notification: true
2025-08-07T10:29:28.448+08:00  INFO 16804 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Registered tools: 3
2025-08-07T10:29:28.448+08:00  INFO 16804 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable resources capabilities, notification: true
2025-08-07T10:29:28.450+08:00  INFO 16804 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable prompts capabilities, notification: true
2025-08-07T10:29:28.451+08:00  INFO 16804 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable completions capabilities
2025-08-07T10:29:28.663+08:00  INFO 16804 --- [mcp-server-image-search] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8099 (http) with context path '/'
2025-08-07T10:29:28.681+08:00  INFO 16804 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : Started ImageSearchApplication in 4.402 seconds (process running for 5.193)
2025-08-07T10:30:35.611+08:00  INFO 16804 --- [mcp-server-image-search] [http-nio-8099-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-07T10:30:35.612+08:00  INFO 16804 --- [mcp-server-image-search] [http-nio-8099-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-07T10:30:35.613+08:00  INFO 16804 --- [mcp-server-image-search] [http-nio-8099-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-07T10:30:35.695+08:00  INFO 16804 --- [mcp-server-image-search] [http-nio-8099-exec-1] c.i.i.controller.MinIOController         : 开始上传图片文件: output.png
2025-08-07T10:30:35.696+08:00  WARN 16804 --- [mcp-server-image-search] [http-nio-8099-exec-1] c.i.i.controller.MinIOController         : 文件验证失败: 无效的文件类型，请上传图片文件
2025-08-07T10:32:05.446+08:00  INFO 16804 --- [mcp-server-image-search] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-08-07T10:32:05.730+08:00  INFO 16804 --- [mcp-server-image-search] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-08-07T10:32:09.803+08:00  INFO 2192 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : Starting ImageSearchApplication using Java 17.0.12 with PID 2192 (C:\Users\<USER>\Downloads\image-search\target\classes started by aofu in C:\Users\<USER>\Downloads\image-search)
2025-08-07T10:32:09.807+08:00  INFO 2192 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-07T10:32:10.639+08:00  WARN 2192 --- [mcp-server-image-search] [main] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[cn.iflytek.imagesearch]' package. Please check your configuration.
2025-08-07T10:32:11.353+08:00  INFO 2192 --- [mcp-server-image-search] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8099 (http)
2025-08-07T10:32:11.374+08:00  INFO 2192 --- [mcp-server-image-search] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-07T10:32:11.374+08:00  INFO 2192 --- [mcp-server-image-search] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-07T10:32:11.482+08:00  INFO 2192 --- [mcp-server-image-search] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-07T10:32:11.482+08:00  INFO 2192 --- [mcp-server-image-search] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1618 ms
2025-08-07T10:32:11.941+08:00  INFO 2192 --- [mcp-server-image-search] [main] c.i.i.types.config.MinIOConfig           : MinIO客户端初始化成功，端点: http://localhost:9000, 存储桶: image-storage
2025-08-07T10:32:12.026+08:00 ERROR 2192 --- [mcp-server-image-search] [main] c.i.i.d.service.impl.MinIOServiceImpl    : 初始化MinIO存储桶失败

java.net.ConnectException: Failed to connect to localhost/[0:0:0:0:0:0:0:1]:9000
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517) ~[okhttp-4.12.0.jar:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:842) ~[na:na]
	Suppressed: java.net.ConnectException: Failed to connect to localhost/127.0.0.1:9000
		... 19 common frames omitted
	Caused by: java.net.ConnectException: Connection refused: no further information
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
		at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
		... 18 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method) ~[na:na]
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[na:na]
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[na:na]
	at java.base/java.net.Socket.connect(Socket.java:633) ~[na:na]
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295) ~[okhttp-4.12.0.jar:na]
	... 18 common frames omitted

2025-08-07T10:32:13.073+08:00  INFO 2192 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable tools capabilities, notification: true
2025-08-07T10:32:13.277+08:00  INFO 2192 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Registered tools: 3
2025-08-07T10:32:13.279+08:00  INFO 2192 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable resources capabilities, notification: true
2025-08-07T10:32:13.280+08:00  INFO 2192 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable prompts capabilities, notification: true
2025-08-07T10:32:13.282+08:00  INFO 2192 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable completions capabilities
2025-08-07T10:32:13.517+08:00  INFO 2192 --- [mcp-server-image-search] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8099 (http) with context path '/'
2025-08-07T10:32:13.537+08:00  INFO 2192 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : Started ImageSearchApplication in 4.361 seconds (process running for 5.033)
2025-08-07T10:32:22.663+08:00  INFO 2192 --- [mcp-server-image-search] [http-nio-8099-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-07T10:32:22.663+08:00  INFO 2192 --- [mcp-server-image-search] [http-nio-8099-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-07T10:32:22.665+08:00  INFO 2192 --- [mcp-server-image-search] [http-nio-8099-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-07T10:32:22.742+08:00  INFO 2192 --- [mcp-server-image-search] [http-nio-8099-exec-1] c.i.i.controller.MinIOController         : 开始上传图片文件: output.png
2025-08-07T10:32:22.762+08:00 ERROR 2192 --- [mcp-server-image-search] [http-nio-8099-exec-1] c.i.i.d.service.impl.MinIOServiceImpl    : 上传图片流失败: output.png

java.net.ConnectException: Failed to connect to localhost/[0:0:0:0:0:0:0:1]:9000
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517) ~[okhttp-4.12.0.jar:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:842) ~[na:na]
	Suppressed: java.net.ConnectException: Failed to connect to localhost/127.0.0.1:9000
		... 19 common frames omitted
	Caused by: java.net.ConnectException: Connection refused: no further information
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
		at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
		... 18 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method) ~[na:na]
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[na:na]
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[na:na]
	at java.base/java.net.Socket.connect(Socket.java:633) ~[na:na]
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295) ~[okhttp-4.12.0.jar:na]
	... 18 common frames omitted

2025-08-07T10:32:22.770+08:00 ERROR 2192 --- [mcp-server-image-search] [http-nio-8099-exec-1] c.i.i.d.service.impl.MinIOServiceImpl    : 上传图片文件失败: output.png

java.lang.RuntimeException: 上传图片流失败
	at cn.iflytek.imagesearch.domain.service.minio.impl.MinIOServiceImpl.uploadImage(MinIOServiceImpl.java:102) ~[classes/:na]
	at cn.iflytek.imagesearch.domain.service.minio.impl.MinIOServiceImpl.uploadImage(MinIOServiceImpl.java:78) ~[classes/:na]
	at cn.iflytek.imagesearch.adapter.MinIOController.uploadImage(MinIOController.java:52) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590) ~[tomcat-embed-core-10.1.43.jar:6.0]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658) ~[tomcat-embed-core-10.1.43.jar:6.0]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.9.jar:6.2.9]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.9.jar:6.2.9]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.9.jar:6.2.9]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at java.base/java.lang.Thread.run(Thread.java:842) ~[na:na]
Caused by: java.net.ConnectException: Failed to connect to localhost/[0:0:0:0:0:0:0:1]:9000
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517) ~[okhttp-4.12.0.jar:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	... 1 common frames omitted
	Suppressed: java.net.ConnectException: Failed to connect to localhost/127.0.0.1:9000
		... 19 common frames omitted
	Caused by: java.net.ConnectException: Connection refused: no further information
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
		at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
		... 18 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method) ~[na:na]
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[na:na]
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[na:na]
	at java.base/java.net.Socket.connect(Socket.java:633) ~[na:na]
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295) ~[okhttp-4.12.0.jar:na]
	... 18 common frames omitted

2025-08-07T10:32:22.773+08:00 ERROR 2192 --- [mcp-server-image-search] [http-nio-8099-exec-1] c.i.i.controller.MinIOController         : 图片上传失败: output.png

java.lang.RuntimeException: 上传图片文件失败
	at cn.iflytek.imagesearch.domain.service.minio.impl.MinIOServiceImpl.uploadImage(MinIOServiceImpl.java:81) ~[classes/:na]
	at cn.iflytek.imagesearch.adapter.MinIOController.uploadImage(MinIOController.java:52) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590) ~[tomcat-embed-core-10.1.43.jar:6.0]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658) ~[tomcat-embed-core-10.1.43.jar:6.0]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.9.jar:6.2.9]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.9.jar:6.2.9]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.9.jar:6.2.9]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at java.base/java.lang.Thread.run(Thread.java:842) ~[na:na]
Caused by: java.lang.RuntimeException: 上传图片流失败
	at cn.iflytek.imagesearch.domain.service.minio.impl.MinIOServiceImpl.uploadImage(MinIOServiceImpl.java:102) ~[classes/:na]
	at cn.iflytek.imagesearch.domain.service.minio.impl.MinIOServiceImpl.uploadImage(MinIOServiceImpl.java:78) ~[classes/:na]
	... 51 common frames omitted
Caused by: java.net.ConnectException: Failed to connect to localhost/[0:0:0:0:0:0:0:1]:9000
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517) ~[okhttp-4.12.0.jar:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	... 1 common frames omitted
	Suppressed: java.net.ConnectException: Failed to connect to localhost/127.0.0.1:9000
		... 19 common frames omitted
	Caused by: java.net.ConnectException: Connection refused: no further information
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
		at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
		at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
		at java.base/java.net.Socket.connect(Socket.java:633)
		at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
		... 18 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method) ~[na:na]
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547) ~[na:na]
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[na:na]
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[na:na]
	at java.base/java.net.Socket.connect(Socket.java:633) ~[na:na]
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128) ~[okhttp-4.12.0.jar:na]
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295) ~[okhttp-4.12.0.jar:na]
	... 18 common frames omitted

2025-08-07T10:32:53.075+08:00  INFO 2192 --- [mcp-server-image-search] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-08-07T10:32:53.372+08:00  INFO 2192 --- [mcp-server-image-search] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-08-07T10:34:10.562+08:00  INFO 12884 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : Starting ImageSearchApplication using Java 17.0.12 with PID 12884 (C:\Users\<USER>\Downloads\image-search\target\classes started by aofu in C:\Users\<USER>\Downloads\image-search)
2025-08-07T10:34:10.565+08:00  INFO 12884 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-07T10:34:11.436+08:00  WARN 12884 --- [mcp-server-image-search] [main] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[cn.iflytek.imagesearch]' package. Please check your configuration.
2025-08-07T10:34:12.087+08:00  INFO 12884 --- [mcp-server-image-search] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8099 (http)
2025-08-07T10:34:12.103+08:00  INFO 12884 --- [mcp-server-image-search] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-07T10:34:12.103+08:00  INFO 12884 --- [mcp-server-image-search] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-07T10:34:12.175+08:00  INFO 12884 --- [mcp-server-image-search] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-07T10:34:12.175+08:00  INFO 12884 --- [mcp-server-image-search] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1553 ms
2025-08-07T10:34:12.594+08:00  INFO 12884 --- [mcp-server-image-search] [main] c.i.i.types.config.MinIOConfig           : MinIO客户端初始化成功，端点: http://************:9000, 存储桶: image-storage
2025-08-07T10:34:13.924+08:00  INFO 12884 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable tools capabilities, notification: true
2025-08-07T10:34:14.095+08:00  INFO 12884 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Registered tools: 3
2025-08-07T10:34:14.096+08:00  INFO 12884 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable resources capabilities, notification: true
2025-08-07T10:34:14.099+08:00  INFO 12884 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable prompts capabilities, notification: true
2025-08-07T10:34:14.104+08:00  INFO 12884 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable completions capabilities
2025-08-07T10:34:14.317+08:00  INFO 12884 --- [mcp-server-image-search] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8099 (http) with context path '/'
2025-08-07T10:34:14.335+08:00  INFO 12884 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : Started ImageSearchApplication in 4.452 seconds (process running for 5.097)
2025-08-07T10:34:26.858+08:00  INFO 12884 --- [mcp-server-image-search] [http-nio-8099-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-07T10:34:26.859+08:00  INFO 12884 --- [mcp-server-image-search] [http-nio-8099-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-07T10:34:26.860+08:00  INFO 12884 --- [mcp-server-image-search] [http-nio-8099-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-07T10:34:26.954+08:00  INFO 12884 --- [mcp-server-image-search] [http-nio-8099-exec-1] c.i.i.controller.MinIOController         : 开始上传图片文件: output.png
2025-08-07T10:34:27.113+08:00  INFO 12884 --- [mcp-server-image-search] [http-nio-8099-exec-1] c.i.i.d.service.impl.MinIOServiceImpl    : 图片上传成功: images/feef2112-8825-410a-a26f-ee16ebd1c189.png
2025-08-07T10:34:27.167+08:00  INFO 12884 --- [mcp-server-image-search] [http-nio-8099-exec-1] c.i.i.controller.MinIOController         : 图片上传成功: output.png -> images/feef2112-8825-410a-a26f-ee16ebd1c189.png
2025-08-07T10:34:52.917+08:00  INFO 12884 --- [mcp-server-image-search] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-08-07T10:34:53.493+08:00  INFO 12884 --- [mcp-server-image-search] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-08-07T13:36:00.909+08:00  INFO 25636 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : Starting ImageSearchApplication using Java 17.0.12 with PID 25636 (C:\Users\<USER>\Downloads\image-search\target\classes started by aofu in C:\Users\<USER>\Downloads\image-search)
2025-08-07T13:36:00.913+08:00  INFO 25636 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-07T13:36:01.765+08:00  WARN 25636 --- [mcp-server-image-search] [main] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[cn.iflytek.imagesearch]' package. Please check your configuration.
2025-08-07T13:36:02.447+08:00  INFO 25636 --- [mcp-server-image-search] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8099 (http)
2025-08-07T13:36:02.464+08:00  INFO 25636 --- [mcp-server-image-search] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-07T13:36:02.464+08:00  INFO 25636 --- [mcp-server-image-search] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-07T13:36:02.563+08:00  INFO 25636 --- [mcp-server-image-search] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-07T13:36:02.564+08:00  INFO 25636 --- [mcp-server-image-search] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1591 ms
2025-08-07T13:36:02.983+08:00  INFO 25636 --- [mcp-server-image-search] [main] c.i.i.types.config.MinIOConfig           : MinIO客户端初始化成功，端点: http://************:9000, 存储桶: image-storage
2025-08-07T13:36:04.359+08:00  INFO 25636 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable tools capabilities, notification: true
2025-08-07T13:36:04.536+08:00  INFO 25636 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Registered tools: 3
2025-08-07T13:36:04.536+08:00  INFO 25636 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable resources capabilities, notification: true
2025-08-07T13:36:04.539+08:00  INFO 25636 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable prompts capabilities, notification: true
2025-08-07T13:36:04.541+08:00  INFO 25636 --- [mcp-server-image-search] [main] o.s.a.m.s.a.McpServerAutoConfiguration   : Enable completions capabilities
2025-08-07T13:36:04.764+08:00  INFO 25636 --- [mcp-server-image-search] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8099 (http) with context path '/'
2025-08-07T13:36:04.791+08:00  INFO 25636 --- [mcp-server-image-search] [main] c.i.imagesearch.ImageSearchApplication   : Started ImageSearchApplication in 4.629 seconds (process running for 5.362)
2025-08-07T13:50:39.731+08:00  INFO 25636 --- [mcp-server-image-search] [http-nio-8099-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-07T13:50:39.731+08:00  INFO 25636 --- [mcp-server-image-search] [http-nio-8099-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-07T13:50:39.740+08:00  INFO 25636 --- [mcp-server-image-search] [http-nio-8099-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 9 ms
2025-08-07T13:50:39.836+08:00 ERROR 25636 --- [mcp-server-image-search] [http-nio-8099-exec-1] c.i.i.controller.GlobalExceptionHandler  : 未知异常

org.springframework.web.servlet.resource.NoResourceFoundException: No static resource mcp/messages.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564) ~[tomcat-embed-core-10.1.43.jar:6.0]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658) ~[tomcat-embed-core-10.1.43.jar:6.0]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.9.jar:6.2.9]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.9.jar:6.2.9]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.9.jar:6.2.9]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at java.base/java.lang.Thread.run(Thread.java:842) ~[na:na]

2025-08-07T13:50:39.899+08:00  WARN 25636 --- [mcp-server-image-search] [http-nio-8099-exec-1] .m.m.a.ExceptionHandlerExceptionResolver : Failure in @ExceptionHandler cn.iflytek.imagesearch.adapter.GlobalExceptionHandler#handleException(Exception)

org.springframework.web.HttpMediaTypeNotAcceptableException: No acceptable representation
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:291) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:208) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:136) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:471) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:73) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:182) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1360) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1161) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1106) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564) ~[tomcat-embed-core-10.1.43.jar:6.0]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885) ~[spring-webmvc-6.2.9.jar:6.2.9]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658) ~[tomcat-embed-core-10.1.43.jar:6.0]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.9.jar:6.2.9]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.9.jar:6.2.9]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-6.2.9.jar:6.2.9]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.9.jar:6.2.9]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-10.1.43.jar:10.1.43]
	at java.base/java.lang.Thread.run(Thread.java:842) ~[na:na]

2025-08-07T13:52:57.047+08:00  INFO 25636 --- [mcp-server-image-search] [http-nio-8099-exec-4] i.m.server.McpAsyncServer                : Client initialize request - Protocol: 2025-06-18, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=true], sampling=Sampling[]], Info: Implementation[name=mcp-inspector, version=0.16.2]
2025-08-07T13:52:57.064+08:00  WARN 25636 --- [mcp-server-image-search] [http-nio-8099-exec-4] i.m.server.McpAsyncServer                : Client requested unsupported protocol version: 2025-06-18, so the server will suggest the 2024-11-05 version instead
2025-08-07T13:54:33.542+08:00  INFO 25636 --- [mcp-server-image-search] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
