package cn.iflytek.imagesearch.types.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 向量搜索配置类
 * 
 * 管理向量搜索相关的配置参数，包括向量类型、相似度阈值、
 * 嵌入模型配置等。这些配置可以通过application.yml文件进行设置。
 */
@Data
@Component
@ConfigurationProperties(prefix = "image-search.vector")
public class VectorSearchConfig {

    /**
     * 默认向量类型
     */
    private String defaultVectorType = "CLIP";

    /**
     * 最小相似度阈值
     * 低于此值的搜索结果将被过滤掉
     */
    private float minSimilarityThreshold = 0.1f;

    /**
     * 最大搜索结果数量
     */
    private int maxSearchResults = 1000;

    /**
     * 向量维度
     */
    private int vectorDimension = 512;

    /**
     * 嵌入模型配置
     */
    private EmbeddingConfig embedding = new EmbeddingConfig();

    /**
     * 相似度计算配置
     */
    private SimilarityConfig similarity = new SimilarityConfig();

    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();

    @Data
    public static class EmbeddingConfig {
        /**
         * 是否启用文本嵌入
         */
        private boolean enabled = true;

        /**
         * 嵌入模型类型
         */
        private String modelType = "CLIP";

        /**
         * 模型名称
         */
        private String modelName = "clip-vit-base-patch32";

        /**
         * 模型版本
         */
        private String modelVersion = "1.0";

        /**
         * API端点URL（如果使用外部服务）
         */
        private String apiEndpoint;

        /**
         * API密钥
         */
        private String apiKey;

        /**
         * 请求超时时间（毫秒）
         */
        private int timeoutMs = 10000;

        /**
         * 批处理大小
         */
        private int batchSize = 32;
    }

    @Data
    public static class SimilarityConfig {
        /**
         * 相似度计算方法
         * 支持：cosine（余弦相似度）、euclidean（欧几里得距离）、dot（点积）
         */
        private String method = "cosine";

        /**
         * 是否启用向量归一化
         */
        private boolean normalizeVectors = true;

        /**
         * 是否使用预计算的向量范数
         */
        private boolean usePrecomputedNorm = true;
    }

    @Data
    public static class CacheConfig {
        /**
         * 是否启用查询向量缓存
         */
        private boolean enableQueryCache = true;

        /**
         * 查询缓存过期时间（秒）
         */
        private int queryCacheExpireSeconds = 3600;

        /**
         * 是否启用结果缓存
         */
        private boolean enableResultCache = false;

        /**
         * 结果缓存过期时间（秒）
         */
        private int resultCacheExpireSeconds = 300;

        /**
         * 缓存最大条目数
         */
        private int maxCacheEntries = 1000;
    }
}
