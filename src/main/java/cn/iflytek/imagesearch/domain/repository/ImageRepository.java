package cn.iflytek.imagesearch.domain.repository;

import cn.iflytek.imagesearch.domain.model.entry.ImageEntity;
import cn.iflytek.imagesearch.domain.model.entry.ImageMetadataFilter;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 图片数据访问接口
 * 提供图片实体的基本CRUD操作
 * 注意：这是一个接口定义，具体实现需要根据实际的数据存储方案来完成
 */
public interface ImageRepository {

    /**
     * 根据图片ID查找图片实体
     *
     * @param imageId 图片ID
     * @return 图片实体，如果不存在则返回空Optional
     */
    Optional<ImageEntity> findById(String imageId);

    /**
     * 根据图片URL查找图片实体
     *
     * @param imageUrl 图片URL
     * @return 图片实体，如果不存在则返回空Optional
     */
    Optional<ImageEntity> findByUrl(String imageUrl);

    /**
     * 根据图片ID列表批量查找图片实体
     *
     * @param imageIds 图片ID列表
     * @return 图片实体列表
     */
    List<ImageEntity> findByIds(List<String> imageIds);

    /**
     * 保存图片实体
     *
     * @param imageEntity 图片实体
     * @return 保存后的图片实体
     */
    ImageEntity save(ImageEntity imageEntity);

    /**
     * 批量保存图片实体
     *
     * @param imageEntities 图片实体列表
     * @return 保存后的图片实体列表
     */
    List<ImageEntity> saveAll(List<ImageEntity> imageEntities);

    /**
     * 根据图片ID删除图片实体
     *
     * @param imageId 图片ID
     * @return 是否删除成功
     */
    boolean deleteById(String imageId);

    /**
     * 检查图片是否存在
     *
     * @param imageId 图片ID
     * @return 是否存在
     */
    boolean existsById(String imageId);

    /**
     * 获取所有图片实体的总数
     *
     * @return 图片总数
     */
    long count();

    /**
     * 分页查询图片实体
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 图片实体列表
     */
    List<ImageEntity> findAll(int offset, int limit);

    /**
     * 根据元数据筛选条件查找图片ID集合
     *
     * @param filter 元数据筛选条件
     * @return 符合条件的图片ID集合
     */
    Set<String> findIdsByMetadataFilter(ImageMetadataFilter filter);

    /**
     * 根据元数据筛选条件查找图片实体列表
     *
     * @param filter 元数据筛选条件
     * @return 符合条件的图片实体列表
     */
    List<ImageEntity> findByMetadataFilter(ImageMetadataFilter filter);
}
