package cn.iflytek.imagesearch.domain.repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 向量数据访问接口
 * 
 * 提供图片特征向量的存储、查询和管理功能，为向量搜索服务提供数据支持。
 * 该接口定义了向量数据的基本CRUD操作和向量搜索相关的数据访问方法。
 * 
 * 主要功能：
 * 1. 向量数据的基本CRUD操作
 * 2. 支持多种向量类型的存储和查询
 * 3. 提供向量搜索所需的批量查询功能
 * 4. 支持向量质量管理和版本控制
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-01
 */
public interface VectorRepository {

    /**
     * 向量实体类
     * 封装向量相关的所有信息
     */
    class VectorEntity {
        private Long id;
        private String imageId;
        private String vectorType;
        private Integer vectorDimension;
        private float[] featureVector;
        private Float vectorNorm;
        private String extractionModel;
        private String extractionVersion;
        private Float qualityScore;
        
        // 构造函数
        public VectorEntity() {}
        
        public VectorEntity(String imageId, String vectorType, float[] featureVector) {
            this.imageId = imageId;
            this.vectorType = vectorType;
            this.featureVector = featureVector;
            this.vectorDimension = featureVector != null ? featureVector.length : 0;
        }
        
        // Getter和Setter方法
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getImageId() { return imageId; }
        public void setImageId(String imageId) { this.imageId = imageId; }
        
        public String getVectorType() { return vectorType; }
        public void setVectorType(String vectorType) { this.vectorType = vectorType; }
        
        public Integer getVectorDimension() { return vectorDimension; }
        public void setVectorDimension(Integer vectorDimension) { this.vectorDimension = vectorDimension; }
        
        public float[] getFeatureVector() { return featureVector; }
        public void setFeatureVector(float[] featureVector) { 
            this.featureVector = featureVector;
            this.vectorDimension = featureVector != null ? featureVector.length : 0;
        }
        
        public Float getVectorNorm() { return vectorNorm; }
        public void setVectorNorm(Float vectorNorm) { this.vectorNorm = vectorNorm; }
        
        public String getExtractionModel() { return extractionModel; }
        public void setExtractionModel(String extractionModel) { this.extractionModel = extractionModel; }
        
        public String getExtractionVersion() { return extractionVersion; }
        public void setExtractionVersion(String extractionVersion) { this.extractionVersion = extractionVersion; }
        
        public Float getQualityScore() { return qualityScore; }
        public void setQualityScore(Float qualityScore) { this.qualityScore = qualityScore; }
    }

    /**
     * 根据图片ID和向量类型查找向量实体
     *
     * @param imageId 图片ID
     * @param vectorType 向量类型，如"CLIP"、"ResNet"等
     * @return 向量实体，如果不存在则返回空Optional
     */
    Optional<VectorEntity> findByImageIdAndType(String imageId, String vectorType);

    /**
     * 根据图片ID查找所有向量实体
     *
     * @param imageId 图片ID
     * @return 向量实体列表
     */
    List<VectorEntity> findByImageId(String imageId);

    /**
     * 根据图片ID列表批量查找向量实体
     *
     * @param imageIds 图片ID列表
     * @param vectorType 向量类型，为null时查询所有类型
     * @return 向量实体列表
     */
    List<VectorEntity> findByImageIds(List<String> imageIds, String vectorType);

    /**
     * 根据向量类型查找所有向量实体
     * 用于全库向量搜索
     *
     * @param vectorType 向量类型
     * @param limit 限制数量，0表示不限制
     * @return 向量实体列表
     */
    List<VectorEntity> findByVectorType(String vectorType, int limit);

    /**
     * 根据向量类型和候选图片ID列表查找向量实体
     * 用于候选集内的向量搜索
     *
     * @param vectorType 向量类型
     * @param candidateIds 候选图片ID集合
     * @return 向量实体列表
     */
    List<VectorEntity> findByVectorTypeAndCandidates(String vectorType, Set<String> candidateIds);

    /**
     * 保存向量实体
     *
     * @param vectorEntity 向量实体
     * @return 保存后的向量实体
     */
    VectorEntity save(VectorEntity vectorEntity);

    /**
     * 批量保存向量实体
     *
     * @param vectorEntities 向量实体列表
     * @return 保存后的向量实体列表
     */
    List<VectorEntity> saveAll(List<VectorEntity> vectorEntities);

    /**
     * 根据图片ID和向量类型删除向量实体
     *
     * @param imageId 图片ID
     * @param vectorType 向量类型
     * @return 是否删除成功
     */
    boolean deleteByImageIdAndType(String imageId, String vectorType);

    /**
     * 根据图片ID删除所有向量实体
     *
     * @param imageId 图片ID
     * @return 删除的记录数
     */
    int deleteByImageId(String imageId);

    /**
     * 检查向量是否存在
     *
     * @param imageId 图片ID
     * @param vectorType 向量类型
     * @return 是否存在
     */
    boolean existsByImageIdAndType(String imageId, String vectorType);

    /**
     * 获取指定向量类型的记录总数
     *
     * @param vectorType 向量类型，为null时统计所有类型
     * @return 记录总数
     */
    long countByVectorType(String vectorType);

    /**
     * 获取所有向量类型列表
     *
     * @return 向量类型列表
     */
    List<String> findAllVectorTypes();
}
