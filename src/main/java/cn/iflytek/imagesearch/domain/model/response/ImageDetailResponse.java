package cn.iflytek.imagesearch.domain.model.response;

import cn.iflytek.imagesearch.domain.model.entry.ImageEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 图片详情响应
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ImageDetailResponse {

    @JsonProperty("imageEntity")
    @JsonPropertyDescription("图片详细信息，包含图片的所有元数据、特征向量和评分信息")
    private ImageEntity imageEntity;

    @JsonProperty("errorMessage")
    @JsonPropertyDescription("错误信息，当操作失败时返回具体的错误描述")
    private String errorMessage;

    @JsonProperty("success")
    @JsonPropertyDescription("操作是否成功的标识，true表示成功，false表示失败")
    private Boolean success = true;
}
