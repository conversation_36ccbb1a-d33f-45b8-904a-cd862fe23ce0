package cn.iflytek.imagesearch.domain.model.entry;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 图片元数据信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ImageMetadata {

    @JsonProperty("width")
    @JsonPropertyDescription("图片宽度，单位像素")
    private int width;

    @JsonProperty("height")
    @JsonPropertyDescription("图片高度，单位像素")
    private int height;

    @JsonProperty("fileSize")
    @JsonPropertyDescription("文件大小，单位字节")
    private long fileSize;

    @JsonProperty("format")
    @JsonPropertyDescription("图片格式，如jpg、png、gif等")
    private String format;

    @JsonProperty("uploadTime")
    @JsonPropertyDescription("图片上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadTime;

    @JsonProperty("tags")
    @JsonPropertyDescription("图片标签列表，用于分类和搜索")
    private List<String> tags;

    @JsonProperty("dominantColor")
    @JsonPropertyDescription("图片主色调，十六进制颜色值，如#FF0000")
    private String dominantColor;
}
