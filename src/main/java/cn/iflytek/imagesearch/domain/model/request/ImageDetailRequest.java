package cn.iflytek.imagesearch.domain.model.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

/**
 * 获取图片详情请求
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ImageDetailRequest {

    @JsonProperty("imageId")
    @JsonPropertyDescription("图片唯一标识符，用于精确定位图片")
    private String imageId;

    @JsonProperty("imageUrl")
    @JsonPropertyDescription("图片URL地址，当没有imageId时可通过URL获取图片信息")
    private String imageUrl;

    @JsonProperty("includeExtendedInfo")
    @JsonPropertyDescription("是否包含扩展信息，如EXIF数据、颜色分析、拍摄参数等详细元数据，默认false")
    private Boolean includeExtendedInfo = false;

    @JsonProperty("includeSimilarImages")
    @JsonPropertyDescription("是否包含相似图片推荐，基于图片特征向量计算相似度，默认false")
    private Boolean includeSimilarImages = false;

    @JsonProperty("similarImagesLimit")
    @JsonPropertyDescription("相似图片推荐数量限制，默认5张，最大不超过20张")
    private Integer similarImagesLimit = 5;
}
