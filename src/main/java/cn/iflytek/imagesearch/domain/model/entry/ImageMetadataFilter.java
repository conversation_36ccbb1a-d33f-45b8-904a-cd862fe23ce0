package cn.iflytek.imagesearch.domain.model.entry;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 图片元数据筛选条件
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ImageMetadataFilter {

    @JsonProperty("sizeRange")
    @JsonPropertyDescription("图片尺寸范围筛选条件")
    private SizeRange sizeRange;

    @JsonProperty("minFileSize")
    @JsonPropertyDescription("最小文件大小，单位字节")
    private Long minFileSize;

    @JsonProperty("maxFileSize")
    @JsonPropertyDescription("最大文件大小，单位字节")
    private Long maxFileSize;

    @JsonProperty("formats")
    @JsonPropertyDescription("支持的图片格式列表，如['jpg', 'png', 'gif']")
    private List<String> formats;

    @JsonProperty("dateRange")
    @JsonPropertyDescription("上传时间范围筛选条件")
    private DateRange dateRange;

    @JsonProperty("tags")
    @JsonPropertyDescription("必须包含的标签列表，图片必须包含所有指定标签")
    private List<String> tags;

    @JsonProperty("aspectRatio")
    @JsonPropertyDescription("宽高比范围筛选条件")
    private AspectRatioRange aspectRatio;

    /**
     * 尺寸范围筛选条件
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SizeRange {

        @JsonProperty("minWidth")
        @JsonPropertyDescription("最小宽度，单位像素")
        private Integer minWidth;

        @JsonProperty("maxWidth")
        @JsonPropertyDescription("最大宽度，单位像素")
        private Integer maxWidth;

        @JsonProperty("minHeight")
        @JsonPropertyDescription("最小高度，单位像素")
        private Integer minHeight;

        @JsonProperty("maxHeight")
        @JsonPropertyDescription("最大高度，单位像素")
        private Integer maxHeight;
    }

    /**
     * 日期范围筛选条件
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class DateRange {

        @JsonProperty("startDate")
        @JsonPropertyDescription("开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startDate;

        @JsonProperty("endDate")
        @JsonPropertyDescription("结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endDate;
    }

    /**
     * 宽高比范围筛选条件
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AspectRatioRange {

        @JsonProperty("minRatio")
        @JsonPropertyDescription("最小宽高比，例如0.5表示高度是宽度的2倍")
        private Double minRatio;

        @JsonProperty("maxRatio")
        @JsonPropertyDescription("最大宽高比，例如2.0表示宽度是高度的2倍")
        private Double maxRatio;
    }


    // 判断筛选条件是否为空
    public boolean isEmpty() {
        return sizeRange == null &&
                minFileSize == null &&
                maxFileSize == null &&
                (formats == null || formats.isEmpty()) &&
                dateRange == null &&
                (tags == null || tags.isEmpty()) &&
                aspectRatio == null;
    }

}
