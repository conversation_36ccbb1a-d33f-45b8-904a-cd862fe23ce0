package cn.iflytek.imagesearch.domain.model.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 图片搜索响应
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SearchResult<T> {

    @JsonProperty("results")
    @JsonPropertyDescription("搜索结果列表，包含匹配的图片实体信息")
    private List<T> results;

    @JsonProperty("totalCount")
    @JsonPropertyDescription("符合条件的图片总数量，用于分页计算")
    private long totalCount;

    @JsonProperty("searchTime")
    @JsonPropertyDescription("搜索耗时，单位毫秒，用于性能监控")
    private long searchTime;
}
