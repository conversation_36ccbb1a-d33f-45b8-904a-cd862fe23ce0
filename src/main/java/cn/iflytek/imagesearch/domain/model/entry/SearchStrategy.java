package cn.iflytek.imagesearch.domain.model.entry;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;

/**
 * 搜索策略枚举
 */
public enum SearchStrategy {

    @JsonProperty("SEMANTIC_ONLY")
    @JsonPropertyDescription("仅语义搜索：只基于文本描述进行向量相似度搜索，不考虑元数据筛选")
    SEMANTIC_ONLY,

    @JsonProperty("METADATA_ONLY")
    @JsonPropertyDescription("仅元数据筛选：只基于结构化条件（尺寸、格式、时间等）进行筛选，不进行语义匹配")
    METADATA_ONLY,

    @JsonProperty("HYBRID")
    @JsonPropertyDescription("混合搜索：并行执行语义搜索和元数据筛选，然后融合评分排序")
    HYBRID,

    @JsonProperty("SEMANTIC_FIRST")
    @JsonPropertyDescription("语义优先：先进行语义搜索获取候选集，再应用元数据筛选")
    SEMANTIC_FIRST,

    @JsonProperty("METADATA_FIRST")
    @JsonPropertyDescription("元数据优先：先进行元数据筛选获取候选集，再进行语义搜索")
    METADATA_FIRST
}
