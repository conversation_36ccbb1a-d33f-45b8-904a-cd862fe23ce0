package cn.iflytek.imagesearch.domain.model.entry;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 图片信息实体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ImageEntity {

    @JsonProperty("id")
    @JsonPropertyDescription("图片唯一标识符")
    private String id;

    @JsonProperty("url")
    @JsonPropertyDescription("图片访问URL地址")
    private String url;

    @JsonProperty("metadata")
    @JsonPropertyDescription("图片元数据信息，包含尺寸、格式、标签等结构化信息")
    private ImageMetadata metadata;

    @JsonProperty("featureVector")
    @JsonPropertyDescription("图片特征向量，用于语义相似度计算，通常不在API响应中返回")
    private float[] featureVector;

    @JsonProperty("semanticScore")
    @JsonPropertyDescription("语义相似度得分，范围0.0-1.0，值越高表示与查询越相关")
    private float semanticScore;

    @JsonProperty("metadataScore")
    @JsonPropertyDescription("元数据匹配得分，范围0.0-1.0，值越高表示与筛选条件越匹配")
    private float metadataScore;

    @JsonProperty("combinedScore")
    @JsonPropertyDescription("综合得分，结合语义和元数据得分的加权平均值")
    private float combinedScore;
}
