package cn.iflytek.imagesearch.domain.service.search.example;

import cn.iflytek.imagesearch.domain.model.entry.ImageEntity;
import cn.iflytek.imagesearch.domain.model.entry.ImageMetadataFilter;
import cn.iflytek.imagesearch.domain.service.search.MetadataSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * MetadataSearchService使用示例
 * 
 * 展示如何使用MetadataSearchService进行各种类型的元数据筛选查询。
 * 包含常见的使用场景和最佳实践。
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-01
 */
@Slf4j
@Component
public class MetadataSearchExample {

    @Autowired
    private MetadataSearchService metadataSearchService;

    /**
     * 示例1：基于图片尺寸的筛选
     * 查找宽度大于1920像素，高度大于1080像素的高清图片
     */
    public Set<String> findHighResolutionImages() {
        log.info("开始查找高清图片...");
        
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .sizeRange(ImageMetadataFilter.SizeRange.builder()
                        .minWidth(1920)
                        .minHeight(1080)
                        .build())
                .build();

        Set<String> imageIds = metadataSearchService.filterByMetadata(filter);
        log.info("找到 {} 张高清图片", imageIds.size());
        
        return imageIds;
    }

    /**
     * 示例2：基于文件大小的筛选
     * 查找文件大小在1MB到10MB之间的图片
     */
    public List<ImageEntity> findMediumSizeImages() {
        log.info("开始查找中等大小的图片...");
        
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .minFileSize(1024 * 1024L)      // 1MB
                .maxFileSize(10 * 1024 * 1024L) // 10MB
                .build();

        List<ImageEntity> images = metadataSearchService.buildSpecification(filter);
        log.info("找到 {} 张中等大小的图片", images.size());
        
        return images;
    }

    /**
     * 示例3：基于图片格式的筛选
     * 查找JPG和PNG格式的图片
     */
    public Set<String> findCommonFormatImages() {
        log.info("开始查找常见格式的图片...");
        
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .formats(Arrays.asList("jpg", "jpeg", "png"))
                .build();

        Set<String> imageIds = metadataSearchService.filterByMetadata(filter);
        log.info("找到 {} 张常见格式的图片", imageIds.size());
        
        return imageIds;
    }

    /**
     * 示例4：基于标签的筛选
     * 查找同时包含"nature"和"landscape"标签的图片
     */
    public List<ImageEntity> findNatureLandscapeImages() {
        log.info("开始查找自然风景图片...");
        
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .tags(Arrays.asList("nature", "landscape"))
                .build();

        List<ImageEntity> images = metadataSearchService.buildSpecification(filter);
        log.info("找到 {} 张自然风景图片", images.size());
        
        return images;
    }

    /**
     * 示例5：基于上传时间的筛选
     * 查找最近30天内上传的图片
     */
    public Set<String> findRecentImages() {
        log.info("开始查找最近上传的图片...");
        
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
        LocalDateTime now = LocalDateTime.now();
        
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .dateRange(ImageMetadataFilter.DateRange.builder()
                        .startDate(thirtyDaysAgo)
                        .endDate(now)
                        .build())
                .build();

        Set<String> imageIds = metadataSearchService.filterByMetadata(filter);
        log.info("找到 {} 张最近上传的图片", imageIds.size());
        
        return imageIds;
    }

    /**
     * 示例6：基于宽高比的筛选
     * 查找宽屏图片（宽高比在1.5到2.5之间）
     */
    public List<ImageEntity> findWideScreenImages() {
        log.info("开始查找宽屏图片...");
        
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .aspectRatio(ImageMetadataFilter.AspectRatioRange.builder()
                        .minRatio(1.5)
                        .maxRatio(2.5)
                        .build())
                .build();

        List<ImageEntity> images = metadataSearchService.buildSpecification(filter);
        log.info("找到 {} 张宽屏图片", images.size());
        
        return images;
    }

    /**
     * 示例7：复合条件筛选
     * 查找高质量的自然风景图片：
     * - 尺寸：至少1920x1080
     * - 格式：JPG或PNG
     * - 标签：包含nature
     * - 文件大小：至少2MB
     * - 上传时间：最近一年内
     */
    public List<ImageEntity> findHighQualityNatureImages() {
        log.info("开始查找高质量自然图片...");
        
        LocalDateTime oneYearAgo = LocalDateTime.now().minusYears(1);
        LocalDateTime now = LocalDateTime.now();
        
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .sizeRange(ImageMetadataFilter.SizeRange.builder()
                        .minWidth(1920)
                        .minHeight(1080)
                        .build())
                .formats(Arrays.asList("jpg", "jpeg", "png"))
                .tags(Arrays.asList("nature"))
                .minFileSize(2 * 1024 * 1024L) // 2MB
                .dateRange(ImageMetadataFilter.DateRange.builder()
                        .startDate(oneYearAgo)
                        .endDate(now)
                        .build())
                .build();

        List<ImageEntity> images = metadataSearchService.buildSpecification(filter);
        log.info("找到 {} 张高质量自然图片", images.size());
        
        return images;
    }

    /**
     * 示例8：移动端适配图片筛选
     * 查找适合移动端显示的图片：
     * - 宽高比：接近9:16（竖屏）
     * - 文件大小：不超过5MB
     * - 格式：JPG或WebP
     */
    public Set<String> findMobileOptimizedImages() {
        log.info("开始查找移动端优化图片...");
        
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .aspectRatio(ImageMetadataFilter.AspectRatioRange.builder()
                        .minRatio(0.4)  // 接近9:16 ≈ 0.56
                        .maxRatio(0.8)
                        .build())
                .maxFileSize(5 * 1024 * 1024L) // 5MB
                .formats(Arrays.asList("jpg", "jpeg", "webp"))
                .build();

        Set<String> imageIds = metadataSearchService.filterByMetadata(filter);
        log.info("找到 {} 张移动端优化图片", imageIds.size());
        
        return imageIds;
    }

    /**
     * 示例9：缩略图候选筛选
     * 查找适合作为缩略图的小尺寸图片：
     * - 尺寸：200x200到800x800之间
     * - 宽高比：接近1:1（正方形）
     * - 文件大小：不超过1MB
     */
    public List<ImageEntity> findThumbnailCandidates() {
        log.info("开始查找缩略图候选图片...");
        
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .sizeRange(ImageMetadataFilter.SizeRange.builder()
                        .minWidth(200)
                        .maxWidth(800)
                        .minHeight(200)
                        .maxHeight(800)
                        .build())
                .aspectRatio(ImageMetadataFilter.AspectRatioRange.builder()
                        .minRatio(0.8)  // 接近1:1
                        .maxRatio(1.25)
                        .build())
                .maxFileSize(1024 * 1024L) // 1MB
                .build();

        List<ImageEntity> images = metadataSearchService.buildSpecification(filter);
        log.info("找到 {} 张缩略图候选图片", images.size());
        
        return images;
    }

    /**
     * 示例10：性能优化 - 先筛选ID再获取详情
     * 演示如何先使用filterByMetadata获取ID集合，再根据需要获取详细信息
     */
    public List<ImageEntity> findImagesWithTwoStepProcess() {
        log.info("开始执行两步筛选流程...");
        
        // 第一步：快速筛选获取ID
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .formats(Arrays.asList("jpg", "png"))
                .minFileSize(1024 * 1024L)
                .build();

        Set<String> imageIds = metadataSearchService.filterByMetadata(filter);
        log.info("第一步筛选完成，找到 {} 个候选图片ID", imageIds.size());
        
        // 第二步：根据业务需要进一步处理
        // 这里可以与其他搜索结果做交集，或者进行其他业务逻辑处理
        
        // 如果需要完整信息，再调用buildSpecification
        if (!imageIds.isEmpty()) {
            List<ImageEntity> images = metadataSearchService.buildSpecification(filter);
            log.info("第二步获取详情完成，返回 {} 张图片的完整信息", images.size());
            return images;
        }
        
        return List.of();
    }
}
