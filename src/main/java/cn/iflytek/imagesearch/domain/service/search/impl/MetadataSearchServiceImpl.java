package cn.iflytek.imagesearch.domain.service.search.impl;

import cn.iflytek.imagesearch.domain.model.entry.ImageEntity;
import cn.iflytek.imagesearch.domain.model.entry.ImageMetadataFilter;
import cn.iflytek.imagesearch.domain.repository.ImageRepository;
import cn.iflytek.imagesearch.domain.service.search.MetadataSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 图片元数据搜索服务实现类
 * 
 * 提供基于图片元数据的筛选和搜索功能，支持多种筛选条件的组合查询。
 * 通过调用ImageRepository的相关方法实现数据访问，并提供必要的业务逻辑处理。
 * 
 * 主要特性：
 * 1. 支持多维度筛选条件的组合查询
 * 2. 提供高性能的ID筛选和完整实体查询两种模式
 * 3. 包含完善的参数验证和异常处理
 * 4. 支持空筛选条件的快速返回
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-01
 */
@Slf4j
@Service
public class MetadataSearchServiceImpl implements MetadataSearchService {

    @Autowired
    private ImageRepository imageRepository;

    /**
     * 根据元数据筛选条件获取符合条件的图片ID集合
     * 
     * 此方法主要用于快速筛选，只返回图片ID，不加载完整的图片实体信息。
     * 适用于需要与其他搜索结果进行交集运算的场景。
     * 
     * 实现逻辑：
     * 1. 验证筛选条件的有效性
     * 2. 如果筛选条件为空，返回空集合
     * 3. 调用Repository层进行数据查询
     * 4. 记录查询日志并返回结果
     * 
     * @param filter 元数据筛选条件，包含各种筛选维度的条件设置
     * @return 符合筛选条件的图片ID集合，如果没有符合条件的图片则返回空集合
     * @throws IllegalArgumentException 当筛选条件参数无效时抛出
     */
    @Override
    public Set<String> filterByMetadata(ImageMetadataFilter filter) {
        log.debug("开始执行元数据筛选，筛选条件: {}", filter);
        
        try {
            // 参数验证
            if (filter == null) {
                log.warn("筛选条件为null，返回空结果");
                return new HashSet<>();
            }

            // 检查筛选条件是否为空
            if (filter.isEmpty()) {
                log.debug("筛选条件为空，返回空结果");
                return new HashSet<>();
            }

            // 验证筛选条件的合理性
            validateFilter(filter);

            // 执行筛选查询
            Set<String> imageIds = imageRepository.findIdsByMetadataFilter(filter);
            
            log.info("元数据筛选完成，筛选条件: {}, 结果数量: {}", 
                    summarizeFilter(filter), imageIds.size());
            
            return imageIds;
            
        } catch (IllegalArgumentException e) {
            log.error("元数据筛选参数无效: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("元数据筛选执行失败", e);
            throw new RuntimeException("元数据筛选执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据元数据筛选条件构建完整的图片实体列表
     * 
     * 此方法返回完整的图片实体信息，包括图片的所有元数据、特征向量和评分信息。
     * 适用于直接展示搜索结果的场景。
     * 
     * 实现逻辑：
     * 1. 验证筛选条件的有效性
     * 2. 如果筛选条件为空，返回空列表
     * 3. 调用Repository层进行数据查询
     * 4. 记录查询日志并返回结果
     * 
     * @param filter 元数据筛选条件，与filterByMetadata方法使用相同的筛选条件
     * @return 符合筛选条件的图片实体列表，包含完整的图片信息，如果没有符合条件的图片则返回空列表
     * @throws IllegalArgumentException 当筛选条件参数无效时抛出
     */
    @Override
    public List<ImageEntity> buildSpecification(ImageMetadataFilter filter) {
        log.debug("开始构建元数据筛选规范，筛选条件: {}", filter);
        
        try {
            // 参数验证
            if (filter == null) {
                log.warn("筛选条件为null，返回空结果");
                return List.of();
            }

            // 检查筛选条件是否为空
            if (filter.isEmpty()) {
                log.debug("筛选条件为空，返回空结果");
                return List.of();
            }

            // 验证筛选条件的合理性
            validateFilter(filter);

            // 执行筛选查询
            List<ImageEntity> imageEntities = imageRepository.findByMetadataFilter(filter);
            
            log.info("元数据筛选规范构建完成，筛选条件: {}, 结果数量: {}", 
                    summarizeFilter(filter), imageEntities.size());
            
            return imageEntities;
            
        } catch (IllegalArgumentException e) {
            log.error("元数据筛选规范构建参数无效: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("元数据筛选规范构建失败", e);
            throw new RuntimeException("元数据筛选规范构建失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证筛选条件的合理性
     * 
     * @param filter 筛选条件
     * @throws IllegalArgumentException 当筛选条件不合理时抛出
     */
    private void validateFilter(ImageMetadataFilter filter) {
        // 验证尺寸范围
        if (filter.getSizeRange() != null) {
            ImageMetadataFilter.SizeRange sizeRange = filter.getSizeRange();
            if (sizeRange.getMinWidth() != null && sizeRange.getMinWidth() < 0) {
                throw new IllegalArgumentException("最小宽度不能为负数");
            }
            if (sizeRange.getMinHeight() != null && sizeRange.getMinHeight() < 0) {
                throw new IllegalArgumentException("最小高度不能为负数");
            }
            if (sizeRange.getMinWidth() != null && sizeRange.getMaxWidth() != null 
                && sizeRange.getMinWidth() > sizeRange.getMaxWidth()) {
                throw new IllegalArgumentException("最小宽度不能大于最大宽度");
            }
            if (sizeRange.getMinHeight() != null && sizeRange.getMaxHeight() != null 
                && sizeRange.getMinHeight() > sizeRange.getMaxHeight()) {
                throw new IllegalArgumentException("最小高度不能大于最大高度");
            }
        }

        // 验证文件大小范围
        if (filter.getMinFileSize() != null && filter.getMinFileSize() < 0) {
            throw new IllegalArgumentException("最小文件大小不能为负数");
        }
        if (filter.getMinFileSize() != null && filter.getMaxFileSize() != null 
            && filter.getMinFileSize() > filter.getMaxFileSize()) {
            throw new IllegalArgumentException("最小文件大小不能大于最大文件大小");
        }

        // 验证日期范围
        if (filter.getDateRange() != null) {
            ImageMetadataFilter.DateRange dateRange = filter.getDateRange();
            if (dateRange.getStartDate() != null && dateRange.getEndDate() != null 
                && dateRange.getStartDate().isAfter(dateRange.getEndDate())) {
                throw new IllegalArgumentException("开始时间不能晚于结束时间");
            }
        }

        // 验证宽高比范围
        if (filter.getAspectRatio() != null) {
            ImageMetadataFilter.AspectRatioRange aspectRatio = filter.getAspectRatio();
            if (aspectRatio.getMinRatio() != null && aspectRatio.getMinRatio() <= 0) {
                throw new IllegalArgumentException("最小宽高比必须大于0");
            }
            if (aspectRatio.getMinRatio() != null && aspectRatio.getMaxRatio() != null 
                && aspectRatio.getMinRatio() > aspectRatio.getMaxRatio()) {
                throw new IllegalArgumentException("最小宽高比不能大于最大宽高比");
            }
        }

        // 验证标签列表
        if (filter.getTags() != null && filter.getTags().size() > 50) {
            throw new IllegalArgumentException("标签数量不能超过50个");
        }

        // 验证格式列表
        if (filter.getFormats() != null && filter.getFormats().size() > 20) {
            throw new IllegalArgumentException("格式数量不能超过20个");
        }
    }

    /**
     * 生成筛选条件的摘要信息，用于日志记录
     * 
     * @param filter 筛选条件
     * @return 筛选条件摘要
     */
    private String summarizeFilter(ImageMetadataFilter filter) {
        StringBuilder summary = new StringBuilder();
        
        if (filter.getSizeRange() != null) {
            summary.append("尺寸筛选,");
        }
        if (filter.getMinFileSize() != null || filter.getMaxFileSize() != null) {
            summary.append("文件大小筛选,");
        }
        if (filter.getFormats() != null && !filter.getFormats().isEmpty()) {
            summary.append("格式筛选(").append(filter.getFormats().size()).append("个),");
        }
        if (filter.getDateRange() != null) {
            summary.append("时间筛选,");
        }
        if (filter.getTags() != null && !filter.getTags().isEmpty()) {
            summary.append("标签筛选(").append(filter.getTags().size()).append("个),");
        }
        if (filter.getAspectRatio() != null) {
            summary.append("宽高比筛选,");
        }
        
        if (summary.length() > 0) {
            summary.setLength(summary.length() - 1); // 移除最后的逗号
        } else {
            summary.append("无筛选条件");
        }
        
        return summary.toString();
    }
}
