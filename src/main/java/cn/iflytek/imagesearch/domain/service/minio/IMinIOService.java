package cn.iflytek.imagesearch.domain.service.minio;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * MinIO对象存储服务接口
 */
public interface IMinIOService {

    /**
     * 上传图片文件
     *
     * @param file 图片文件
     * @param fileName 文件名
     * @return 存储路径
     */
    String uploadImage(MultipartFile file, String fileName);

    /**
     * 上传图片流
     *
     * @param inputStream 输入流
     * @param fileName 文件名
     * @param contentType 内容类型
     * @param size 文件大小
     * @return 存储路径
     */
    String uploadImage(InputStream inputStream, String fileName, String contentType, long size);

    /**
     * 上传缩略图
     *
     * @param inputStream 输入流
     * @param fileName 文件名
     * @param contentType 内容类型
     * @param size 文件大小
     * @return 存储路径
     */
    String uploadThumbnail(InputStream inputStream, String fileName, String contentType, long size);

    /**
     * 获取图片访问URL
     *
     * @param filePath 文件路径
     * @return 访问URL
     */
    String getImageUrl(String filePath);

    /**
     * 获取图片预签名URL
     *
     * @param filePath 文件路径
     * @param expiry 过期时间(秒)
     * @return 预签名URL
     */
    String getPresignedUrl(String filePath, int expiry);

    /**
     * 删除图片文件
     *
     * @param filePath 文件路径
     * @return 是否成功
     */
    boolean deleteImage(String filePath);

    /**
     * 检查文件是否存在
     *
     * @param filePath 文件路径
     * @return 是否存在
     */
    boolean fileExists(String filePath);

    /**
     * 获取文件信息
     *
     * @param filePath 文件路径
     * @return 文件信息
     */
    FileInfo getFileInfo(String filePath);

    /**
     * 文件信息类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    class FileInfo {

        @JsonProperty("fileName")
        @JsonPropertyDescription("文件名称")
        private String fileName;

        @JsonProperty("size")
        @JsonPropertyDescription("文件大小，单位字节")
        private long size;

        @JsonProperty("contentType")
        @JsonPropertyDescription("文件MIME类型，如image/jpeg、image/png等")
        private String contentType;

        @JsonProperty("etag")
        @JsonPropertyDescription("文件ETag，用于文件完整性校验")
        private String etag;

    }
}
