package cn.iflytek.imagesearch.domain.service.search;

import cn.iflytek.imagesearch.domain.model.entry.ImageEntity;
import cn.iflytek.imagesearch.domain.model.entry.ImageMetadataFilter;

import java.util.List;
import java.util.Set;

/**
 * 图片元数据搜索服务接口
 *
 * 提供基于图片元数据的筛选和搜索功能，支持多种筛选条件的组合查询，
 * 包括尺寸、文件大小、格式、上传时间、标签和宽高比等维度的筛选。
 *
 * 主要功能：
 * 1. 根据元数据筛选条件快速获取符合条件的图片ID集合
 * 2. 根据元数据筛选条件构建完整的图片实体列表
 *
 * 使用场景：
 * - 用户通过界面设置筛选条件进行图片搜索
 * - 与语义搜索结合，对语义搜索结果进行进一步筛选
 * - 图片管理和分类功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-01
 */
public interface MetadataSearchService {

    /**
     * 根据元数据筛选条件获取符合条件的图片ID集合
     *
     * 此方法主要用于快速筛选，只返回图片ID，不加载完整的图片实体信息。
     * 适用于需要与其他搜索结果进行交集运算的场景，如语义搜索结果的进一步筛选。
     *
     * 支持的筛选条件：
     * - 图片尺寸范围（宽度、高度）
     * - 文件大小范围
     * - 图片格式列表
     * - 上传时间范围
     * - 标签列表（AND逻辑，图片必须包含所有指定标签）
     * - 宽高比范围
     *
     * @param filter 元数据筛选条件，包含各种筛选维度的条件设置
     * @return 符合筛选条件的图片ID集合，如果没有符合条件的图片则返回空集合
     * @throws IllegalArgumentException 当筛选条件参数无效时抛出
     *
     * @example
     * <pre>
     * ImageMetadataFilter filter = ImageMetadataFilter.builder()
     *     .sizeRange(SizeRange.builder().minWidth(800).minHeight(600).build())
     *     .formats(Arrays.asList("jpg", "png"))
     *     .tags(Arrays.asList("nature", "landscape"))
     *     .build();
     * Set<String> imageIds = metadataSearchService.filterByMetadata(filter);
     * </pre>
     */
    Set<String> filterByMetadata(ImageMetadataFilter filter);

    /**
     * 根据元数据筛选条件构建完整的图片实体列表
     *
     * 此方法返回完整的图片实体信息，包括图片的所有元数据、特征向量和评分信息。
     * 适用于直接展示搜索结果的场景，或需要完整图片信息进行后续处理的情况。
     *
     * 与filterByMetadata方法的区别：
     * - filterByMetadata：只返回图片ID，性能更好，适合做交集运算
     * - buildSpecification：返回完整图片实体，包含所有详细信息
     *
     * 返回的图片列表会按照一定规则排序（通常按上传时间倒序），
     * 调用方可以根据需要进行进一步的排序和分页处理。
     *
     * @param filter 元数据筛选条件，与filterByMetadata方法使用相同的筛选条件
     * @return 符合筛选条件的图片实体列表，包含完整的图片信息，如果没有符合条件的图片则返回空列表
     * @throws IllegalArgumentException 当筛选条件参数无效时抛出
     *
     * @example
     * <pre>
     * ImageMetadataFilter filter = ImageMetadataFilter.builder()
     *     .dateRange(DateRange.builder()
     *         .startDate(LocalDateTime.now().minusDays(30))
     *         .endDate(LocalDateTime.now())
     *         .build())
     *     .minFileSize(1024L * 1024L) // 1MB
     *     .build();
     * List<ImageEntity> images = metadataSearchService.buildSpecification(filter);
     * </pre>
     */
    List<ImageEntity> buildSpecification(ImageMetadataFilter filter);

}
