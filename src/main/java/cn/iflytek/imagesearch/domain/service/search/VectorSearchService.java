package cn.iflytek.imagesearch.domain.service.search;

import cn.iflytek.imagesearch.domain.model.entry.ImageEntity;

import java.util.List;
import java.util.Set;

/**
 * 向量搜索服务接口
 *
 * 提供基于特征向量的语义相似度搜索功能，通过计算查询文本的向量表示与图片特征向量之间的相似度，
 * 实现智能的图片语义搜索。该服务是图片搜索系统的核心组件之一，负责处理所有与向量相似度计算相关的操作。
 *
 * 主要功能：
 * 1. 全库语义搜索：在整个图片库中基于语义相似度进行搜索
 * 2. 候选集语义搜索：在指定的候选图片集合中进行语义搜索
 * 3. 向量相似度计算：计算查询向量与图片特征向量之间的相似度得分
 *
 * 技术特点：
 * - 支持多种向量相似度计算算法（余弦相似度、欧几里得距离等）
 * - 优化的向量检索性能，支持大规模图片库的实时搜索
 * - 与元数据搜索服务协同工作，支持混合搜索策略
 * - 可配置的相似度阈值和搜索参数
 *
 * 使用场景：
 * - 用户输入自然语言描述搜索相关图片
 * - 基于图片内容的智能推荐
 * - 图片去重和相似图片检测
 * - 多模态搜索中的图片匹配
 *
 * 注意事项：
 * - 本服务不负责文本到向量的转换，需要外部提供查询向量
 * - 搜索结果按相似度得分降序排列
 * - 相似度得分范围为0.0-1.0，值越高表示越相似
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-01
 */
public interface VectorSearchService {

    /**
     * 全库语义搜索
     *
     * 基于查询文本在整个图片库中进行语义相似度搜索，返回最相关的图片列表。
     * 该方法会将查询文本转换为向量表示，然后与所有图片的特征向量进行相似度计算，
     * 按相似度得分降序返回最匹配的结果。
     *
     * 搜索流程：
     * 1. 将查询文本转换为特征向量（通过嵌入模型）
     * 2. 计算查询向量与所有图片特征向量的相似度
     * 3. 按相似度得分排序并返回前N个结果
     * 4. 为每个结果设置语义相似度得分
     *
     * 性能优化：
     * - 使用向量索引加速相似度计算
     * - 支持批量向量计算
     * - 可配置的相似度阈值过滤
     *
     * @param query 查询文本，用于描述要搜索的图片内容
     *              例如："一只可爱的小猫"、"蓝色的天空和白云"、"现代建筑风格"等
     *              查询文本应该尽可能具体和描述性，以获得更准确的搜索结果
     * @param limit 返回结果的最大数量，必须大于0
     *              建议值：10-100，过大的值可能影响性能
     *              实际返回数量可能小于limit（当匹配图片数量不足时）
     * @return 按语义相似度降序排列的图片实体列表，每个实体包含：
     *         - 图片基本信息（ID、URL、元数据）
     *         - 语义相似度得分（semanticScore字段，范围0.0-1.0）
     *         - 特征向量（用于后续计算，通常不在API响应中返回）
     *         如果没有找到相关图片或查询无效，返回空列表
     * @throws IllegalArgumentException 当query为null、空字符串或limit <= 0时抛出
     * @throws RuntimeException 当向量计算或数据库查询出现异常时抛出
     *
     * @example
     * <pre>
     * // 搜索猫咪相关的图片
     * List<ImageEntity> results = vectorSearchService.semanticSearch("可爱的小猫", 20);
     * for (ImageEntity image : results) {
     *     System.out.println("图片ID: " + image.getId());
     *     System.out.println("相似度: " + image.getSemanticScore());
     *     System.out.println("URL: " + image.getUrl());
     * }
     * </pre>
     */
    List<ImageEntity> semanticSearch(String query, int limit);

    /**
     * 候选集内语义搜索
     *
     * 在指定的候选图片集合中进行语义相似度搜索，返回最相关的图片列表。
     * 该方法通常用于混合搜索策略中，先通过元数据筛选获得候选集，
     * 然后在候选集内进行精确的语义匹配，以提高搜索精度和性能。
     *
     * 与全库搜索的区别：
     * - 搜索范围限制在指定的候选图片集合内
     * - 性能更好，因为计算量减少
     * - 通常用于多阶段搜索策略
     * - 可以与元数据筛选结果结合使用
     *
     * 搜索流程：
     * 1. 验证候选图片ID集合的有效性
     * 2. 将查询文本转换为特征向量
     * 3. 仅对候选集中的图片计算相似度
     * 4. 按相似度得分排序并返回前N个结果
     *
     * 使用场景：
     * - 元数据优先搜索策略：先筛选后语义匹配
     * - 混合搜索中的精确匹配阶段
     * - 在特定分类或标签下的语义搜索
     * - 用户自定义范围内的智能搜索
     *
     * @param query 查询文本，语义描述要搜索的图片内容
     *              要求与semanticSearch方法相同
     * @param candidateIds 候选图片ID集合，只在这些图片中进行搜索
     *                     - 不能为null或空集合
     *                     - 包含的ID必须是有效的图片标识符
     *                     - 无效的ID会被自动过滤，不影响搜索结果
     *                     - 建议候选集大小在100-10000之间以平衡性能和效果
     * @param limit 返回结果的最大数量，必须大于0
     *              实际返回数量受候选集大小限制，可能小于指定的limit
     * @return 按语义相似度降序排列的图片实体列表，结构与semanticSearch相同
     *         只包含候选集中的图片，如果候选集为空或无匹配结果则返回空列表
     * @throws IllegalArgumentException 当query无效、candidateIds为null/空集合或limit <= 0时抛出
     * @throws RuntimeException 当向量计算或数据库查询出现异常时抛出
     *
     * @example
     * <pre>
     * // 先通过元数据筛选获得候选集
     * Set<String> candidates = metadataSearchService.filterByMetadata(filter);
     *
     * // 在候选集中进行语义搜索
     * List<ImageEntity> results = vectorSearchService.semanticSearchWithinCandidates(
     *     "现代建筑", candidates, 10);
     *
     * // 处理搜索结果
     * results.forEach(image -> {
     *     System.out.println("匹配图片: " + image.getId() +
     *                       ", 相似度: " + image.getSemanticScore());
     * });
     * </pre>
     */
    List<ImageEntity> semanticSearchWithinCandidates(String query, Set<String> candidateIds, int limit);

}
