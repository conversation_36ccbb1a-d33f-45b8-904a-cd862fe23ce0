package cn.iflytek.imagesearch.domain.service.search.impl;

import cn.iflytek.imagesearch.domain.model.entry.ImageEntity;
import cn.iflytek.imagesearch.domain.repository.ImageRepository;
import cn.iflytek.imagesearch.domain.repository.VectorRepository;
import cn.iflytek.imagesearch.domain.service.search.VectorSearchService;
import cn.iflytek.imagesearch.types.config.VectorSearchConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 向量搜索服务实现类
 * 
 * 实现基于特征向量的语义相似度搜索功能。该实现提供了完整的向量搜索逻辑，
 * 包括向量相似度计算、结果排序和评分设置等核心功能。
 * 
 * 注意：本实现不包含文本到向量的转换功能，需要外部提供查询向量。
 * 在实际部署时，需要集成具体的嵌入模型服务（如OpenAI Embeddings、CLIP等）。
 */
@Service
public class VectorSearchServiceImpl implements VectorSearchService {

    private static final Logger log = LoggerFactory.getLogger(VectorSearchServiceImpl.class);

    @Autowired
    private VectorRepository vectorRepository;

    @Autowired
    private ImageRepository imageRepository;

    @Autowired
    private VectorSearchConfig vectorSearchConfig;

    @Override
    public List<ImageEntity> semanticSearch(String query, int limit) {
        log.debug("开始执行全库语义搜索，查询: {}, 限制数量: {}", query, limit);
        
        // 参数验证
        if (query == null || query.trim().isEmpty()) {
            throw new IllegalArgumentException("查询文本不能为空");
        }
        if (limit <= 0) {
            throw new IllegalArgumentException("限制数量必须大于0");
        }

        try {
            // 1. 将查询文本转换为向量（这里使用模拟实现）
            float[] queryVector = convertTextToVector(query);
            if (queryVector == null) {
                log.warn("查询文本转换为向量失败: {}", query);
                return new ArrayList<>();
            }

            // 2. 获取所有图片的特征向量
            List<VectorRepository.VectorEntity> allVectors =
                vectorRepository.findByVectorType(vectorSearchConfig.getDefaultVectorType(), 0);
            
            if (allVectors.isEmpty()) {
                log.info("未找到任何特征向量数据");
                return new ArrayList<>();
            }

            // 3. 计算相似度并排序
            List<SimilarityResult> similarityResults = calculateSimilarities(queryVector, allVectors);
            
            // 4. 过滤低相似度结果并限制数量
            List<SimilarityResult> filteredResults = similarityResults.stream()
                    .filter(result -> result.similarity >= vectorSearchConfig.getMinSimilarityThreshold())
                    .limit(limit)
                    .collect(Collectors.toList());

            // 5. 获取图片实体并设置相似度得分
            return buildImageEntitiesWithScores(filteredResults);

        } catch (Exception e) {
            log.error("全库语义搜索执行失败", e);
            throw new RuntimeException("语义搜索执行失败", e);
        }
    }

    @Override
    public List<ImageEntity> semanticSearchWithinCandidates(String query, Set<String> candidateIds, int limit) {
        log.debug("开始执行候选集语义搜索，查询: {}, 候选数量: {}, 限制数量: {}", 
                 query, candidateIds.size(), limit);
        
        // 参数验证
        if (query == null || query.trim().isEmpty()) {
            throw new IllegalArgumentException("查询文本不能为空");
        }
        if (candidateIds == null || candidateIds.isEmpty()) {
            throw new IllegalArgumentException("候选图片ID集合不能为空");
        }
        if (limit <= 0) {
            throw new IllegalArgumentException("限制数量必须大于0");
        }

        try {
            // 1. 将查询文本转换为向量
            float[] queryVector = convertTextToVector(query);
            if (queryVector == null) {
                log.warn("查询文本转换为向量失败: {}", query);
                return new ArrayList<>();
            }

            // 2. 获取候选集的特征向量
            List<VectorRepository.VectorEntity> candidateVectors =
                vectorRepository.findByVectorTypeAndCandidates(vectorSearchConfig.getDefaultVectorType(), candidateIds);
            
            if (candidateVectors.isEmpty()) {
                log.info("候选集中未找到任何特征向量数据");
                return new ArrayList<>();
            }

            // 3. 计算相似度并排序
            List<SimilarityResult> similarityResults = calculateSimilarities(queryVector, candidateVectors);
            
            // 4. 过滤低相似度结果并限制数量
            List<SimilarityResult> filteredResults = similarityResults.stream()
                    .filter(result -> result.similarity >= vectorSearchConfig.getMinSimilarityThreshold())
                    .limit(limit)
                    .collect(Collectors.toList());

            // 5. 获取图片实体并设置相似度得分
            return buildImageEntitiesWithScores(filteredResults);

        } catch (Exception e) {
            log.error("候选集语义搜索执行失败", e);
            throw new RuntimeException("候选集语义搜索执行失败", e);
        }
    }

    /**
     * 将文本转换为向量
     * 
     * 注意：这是一个模拟实现，实际部署时需要替换为真实的嵌入模型服务调用。
     * 可以集成OpenAI Embeddings、Sentence Transformers、CLIP等模型。
     * 
     * @param text 输入文本
     * @return 特征向量，失败时返回null
     */
    private float[] convertTextToVector(String text) {
        // TODO: 集成真实的文本嵌入模型
        // 这里使用简单的模拟实现，实际应该调用嵌入模型API
        
        log.debug("模拟文本向量转换: {}", text);
        
        // 模拟生成向量（维度从配置获取）
        Random random = new Random(text.hashCode()); // 使用文本哈希作为种子，保证相同文本生成相同向量
        float[] vector = new float[vectorSearchConfig.getVectorDimension()];
        
        for (int i = 0; i < vector.length; i++) {
            vector[i] = (random.nextFloat() - 0.5f) * 2.0f; // 生成[-1, 1]范围的随机数
        }
        
        // 归一化向量
        normalizeVector(vector);
        
        return vector;
    }

    /**
     * 计算查询向量与所有候选向量的相似度
     * 
     * @param queryVector 查询向量
     * @param candidateVectors 候选向量列表
     * @return 相似度结果列表，按相似度降序排列
     */
    private List<SimilarityResult> calculateSimilarities(float[] queryVector, 
                                                        List<VectorRepository.VectorEntity> candidateVectors) {
        List<SimilarityResult> results = new ArrayList<>();
        
        for (VectorRepository.VectorEntity vectorEntity : candidateVectors) {
            float[] candidateVector = vectorEntity.getFeatureVector();
            if (candidateVector == null || candidateVector.length != queryVector.length) {
                log.warn("跳过无效向量，图片ID: {}", vectorEntity.getImageId());
                continue;
            }
            
            // 计算余弦相似度
            float similarity = calculateCosineSimilarity(queryVector, candidateVector);
            results.add(new SimilarityResult(vectorEntity.getImageId(), similarity));
        }
        
        // 按相似度降序排序
        results.sort((a, b) -> Float.compare(b.similarity, a.similarity));
        
        log.debug("计算了 {} 个向量的相似度", results.size());
        return results;
    }

    /**
     * 计算两个向量的余弦相似度
     * 
     * @param vector1 向量1
     * @param vector2 向量2
     * @return 余弦相似度值，范围[-1, 1]，转换为[0, 1]
     */
    private float calculateCosineSimilarity(float[] vector1, float[] vector2) {
        if (vector1.length != vector2.length) {
            throw new IllegalArgumentException("向量维度不匹配");
        }
        
        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;
        
        for (int i = 0; i < vector1.length; i++) {
            dotProduct += vector1[i] * vector2[i];
            norm1 += vector1[i] * vector1[i];
            norm2 += vector2[i] * vector2[i];
        }
        
        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0f;
        }
        
        double cosineSimilarity = dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
        
        // 将余弦相似度从[-1, 1]转换为[0, 1]
        return (float) ((cosineSimilarity + 1.0) / 2.0);
    }

    /**
     * 向量归一化
     * 
     * @param vector 待归一化的向量
     */
    private void normalizeVector(float[] vector) {
        double norm = 0.0;
        for (float value : vector) {
            norm += value * value;
        }
        norm = Math.sqrt(norm);
        
        if (norm > 0) {
            for (int i = 0; i < vector.length; i++) {
                vector[i] = (float) (vector[i] / norm);
            }
        }
    }

    /**
     * 根据相似度结果构建图片实体列表
     * 
     * @param similarityResults 相似度结果列表
     * @return 图片实体列表
     */
    private List<ImageEntity> buildImageEntitiesWithScores(List<SimilarityResult> similarityResults) {
        if (similarityResults.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 提取图片ID列表
        List<String> imageIds = similarityResults.stream()
                .map(result -> result.imageId)
                .collect(Collectors.toList());
        
        // 批量查询图片实体
        List<ImageEntity> imageEntities = imageRepository.findByIds(imageIds);
        
        // 创建ID到实体的映射
        Map<String, ImageEntity> entityMap = imageEntities.stream()
                .collect(Collectors.toMap(ImageEntity::getId, entity -> entity));
        
        // 按相似度结果的顺序构建最终列表，并设置相似度得分
        List<ImageEntity> result = new ArrayList<>();
        for (SimilarityResult similarityResult : similarityResults) {
            ImageEntity entity = entityMap.get(similarityResult.imageId);
            if (entity != null) {
                entity.setSemanticScore(similarityResult.similarity);
                result.add(entity);
            }
        }
        
        log.debug("构建了 {} 个图片实体，设置了语义相似度得分", result.size());
        return result;
    }

    /**
     * 相似度计算结果内部类
     */
    private static class SimilarityResult {
        final String imageId;
        final float similarity;
        
        SimilarityResult(String imageId, float similarity) {
            this.imageId = imageId;
            this.similarity = similarity;
        }
    }
}
