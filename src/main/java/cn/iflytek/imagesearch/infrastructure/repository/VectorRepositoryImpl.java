package cn.iflytek.imagesearch.infrastructure.repository;

import cn.iflytek.imagesearch.domain.repository.VectorRepository;
import cn.iflytek.imagesearch.infrastructure.persistence.mapper.VectorMapper;
import cn.iflytek.imagesearch.infrastructure.persistence.po.VectorPO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 向量仓储接口实现类
 * 
 * 实现向量数据的持久化操作，包括向量的存储、查询和管理。
 * 负责向量实体与持久化对象之间的转换，以及JSON格式向量数据的序列化/反序列化。
 */
@Repository
public class VectorRepositoryImpl implements VectorRepository {

    private static final Logger log = LoggerFactory.getLogger(VectorRepositoryImpl.class);
    
    @Autowired
    private VectorMapper vectorMapper;
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Optional<VectorEntity> findByImageIdAndType(String imageId, String vectorType) {
        if (imageId == null || imageId.trim().isEmpty() || vectorType == null || vectorType.trim().isEmpty()) {
            return Optional.empty();
        }

        VectorPO vectorPO = vectorMapper.selectByImageIdAndType(imageId, vectorType);
        if (vectorPO == null) {
            return Optional.empty();
        }

        VectorEntity vectorEntity = convertToEntity(vectorPO);
        return Optional.ofNullable(vectorEntity);
    }

    @Override
    public List<VectorEntity> findByImageId(String imageId) {
        if (imageId == null || imageId.trim().isEmpty()) {
            return new ArrayList<>();
        }

        List<VectorPO> vectorPOList = vectorMapper.selectByImageId(imageId);
        return vectorPOList.stream()
                .map(this::convertToEntity)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<VectorEntity> findByImageIds(List<String> imageIds, String vectorType) {
        if (imageIds == null || imageIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 过滤空值
        List<String> validIds = imageIds.stream()
                .filter(id -> id != null && !id.trim().isEmpty())
                .collect(Collectors.toList());

        if (validIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<VectorPO> vectorPOList = vectorMapper.selectByImageIds(validIds, vectorType);
        return vectorPOList.stream()
                .map(this::convertToEntity)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<VectorEntity> findByVectorType(String vectorType, int limit) {
        if (vectorType == null || vectorType.trim().isEmpty()) {
            return new ArrayList<>();
        }

        List<VectorPO> vectorPOList = vectorMapper.selectByVectorType(vectorType, limit);
        return vectorPOList.stream()
                .map(this::convertToEntity)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<VectorEntity> findByVectorTypeAndCandidates(String vectorType, Set<String> candidateIds) {
        if (vectorType == null || vectorType.trim().isEmpty() || candidateIds == null || candidateIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 转换为List
        List<String> candidateList = new ArrayList<>(candidateIds);
        List<VectorPO> vectorPOList = vectorMapper.selectByVectorTypeAndCandidates(vectorType, candidateList);
        
        return vectorPOList.stream()
                .map(this::convertToEntity)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public VectorEntity save(VectorEntity vectorEntity) {
        if (vectorEntity == null || vectorEntity.getImageId() == null || vectorEntity.getVectorType() == null) {
            throw new IllegalArgumentException("VectorEntity, imageId and vectorType cannot be null");
        }

        VectorPO vectorPO = convertToPO(vectorEntity);
        if (vectorPO == null) {
            throw new RuntimeException("Failed to convert VectorEntity to VectorPO");
        }

        // 检查是否已存在
        boolean exists = vectorMapper.existsByImageIdAndType(
                vectorEntity.getImageId(), vectorEntity.getVectorType()) > 0;

        if (exists) {
            // 更新记录
            vectorMapper.update(vectorPO);
        } else {
            // 插入记录
            vectorMapper.insert(vectorPO);
            vectorEntity.setId(vectorPO.getId());
        }

        return vectorEntity;
    }

    @Override
    @Transactional
    public List<VectorEntity> saveAll(List<VectorEntity> vectorEntities) {
        if (vectorEntities == null || vectorEntities.isEmpty()) {
            return new ArrayList<>();
        }

        List<VectorEntity> savedEntities = new ArrayList<>();
        for (VectorEntity entity : vectorEntities) {
            if (entity != null && entity.getImageId() != null && entity.getVectorType() != null) {
                savedEntities.add(save(entity));
            }
        }

        return savedEntities;
    }

    @Override
    @Transactional
    public boolean deleteByImageIdAndType(String imageId, String vectorType) {
        if (imageId == null || imageId.trim().isEmpty() || vectorType == null || vectorType.trim().isEmpty()) {
            return false;
        }

        int deletedRows = vectorMapper.deleteByImageIdAndType(imageId, vectorType);
        return deletedRows > 0;
    }

    @Override
    @Transactional
    public int deleteByImageId(String imageId) {
        if (imageId == null || imageId.trim().isEmpty()) {
            return 0;
        }

        return vectorMapper.deleteByImageId(imageId);
    }

    @Override
    public boolean existsByImageIdAndType(String imageId, String vectorType) {
        if (imageId == null || imageId.trim().isEmpty() || vectorType == null || vectorType.trim().isEmpty()) {
            return false;
        }
        return vectorMapper.existsByImageIdAndType(imageId, vectorType) > 0;
    }

    @Override
    public long countByVectorType(String vectorType) {
        return vectorMapper.countByVectorType(vectorType);
    }

    @Override
    public List<String> findAllVectorTypes() {
        return vectorMapper.selectDistinctVectorTypes();
    }

    /**
     * 将VectorPO转换为VectorEntity
     */
    private VectorEntity convertToEntity(VectorPO vectorPO) {
        if (vectorPO == null) {
            return null;
        }

        try {
            VectorEntity entity = new VectorEntity();
            entity.setId(vectorPO.getId());
            entity.setImageId(vectorPO.getImageId());
            entity.setVectorType(vectorPO.getVectorType());
            entity.setVectorDimension(vectorPO.getVectorDimension());
            entity.setVectorNorm(vectorPO.getVectorNorm());
            entity.setExtractionModel(vectorPO.getExtractionModel());
            entity.setExtractionVersion(vectorPO.getExtractionVersion());
            entity.setQualityScore(vectorPO.getQualityScore());

            // 解析JSON格式的向量数据
            if (vectorPO.getFeatureVector() != null && !vectorPO.getFeatureVector().trim().isEmpty()) {
                float[] vector = objectMapper.readValue(vectorPO.getFeatureVector(), float[].class);
                entity.setFeatureVector(vector);
            }

            return entity;
        } catch (JsonProcessingException e) {
            log.error("Failed to parse feature vector JSON for image {}: {}", 
                     vectorPO.getImageId(), e.getMessage());
            return null;
        }
    }

    /**
     * 将VectorEntity转换为VectorPO
     */
    private VectorPO convertToPO(VectorEntity entity) {
        if (entity == null) {
            return null;
        }

        try {
            VectorPO vectorPO = VectorPO.builder()
                    .id(entity.getId())
                    .imageId(entity.getImageId())
                    .vectorType(entity.getVectorType())
                    .vectorDimension(entity.getVectorDimension())
                    .vectorNorm(entity.getVectorNorm())
                    .extractionModel(entity.getExtractionModel())
                    .extractionVersion(entity.getExtractionVersion())
                    .qualityScore(entity.getQualityScore())
                    .build();

            // 序列化向量数据为JSON
            if (entity.getFeatureVector() != null) {
                String vectorJson = objectMapper.writeValueAsString(entity.getFeatureVector());
                vectorPO.setFeatureVector(vectorJson);
                
                // 计算向量范数（如果未设置）
                if (entity.getVectorNorm() == null) {
                    float norm = calculateVectorNorm(entity.getFeatureVector());
                    vectorPO.setVectorNorm(norm);
                    entity.setVectorNorm(norm);
                }
            }

            return vectorPO;
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize feature vector to JSON for image {}: {}", 
                     entity.getImageId(), e.getMessage());
            return null;
        }
    }

    /**
     * 计算向量的L2范数
     */
    private float calculateVectorNorm(float[] vector) {
        if (vector == null || vector.length == 0) {
            return 0.0f;
        }

        double sum = 0.0;
        for (float value : vector) {
            sum += value * value;
        }
        return (float) Math.sqrt(sum);
    }
}
