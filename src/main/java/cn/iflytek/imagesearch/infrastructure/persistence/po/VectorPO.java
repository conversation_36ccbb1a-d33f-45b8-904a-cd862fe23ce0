package cn.iflytek.imagesearch.infrastructure.persistence.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 图片特征向量数据持久化对象
 * 
 * 用于存储图片的特征向量信息，支持多种向量类型和提取模型。
 * 每个图片可以有多个不同类型的特征向量（如CLIP、ResNet等）。
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VectorPO {

    /**
     * 主键ID，自增长
     */
    private Long id;

    /**
     * 图片ID，关联image表的主键
     */
    private String imageId;

    /**
     * 向量类型，如CLIP、ResNet、VGG等
     * 用于区分不同的特征提取模型产生的向量
     */
    private String vectorType;

    /**
     * 向量维度，如512、1024、2048等
     * 不同模型产生的向量维度可能不同
     */
    private Integer vectorDimension;

    /**
     * 特征向量，JSON数组格式存储
     * 例如：[0.1, 0.2, -0.3, 0.4, ...]
     */
    private String featureVector;

    /**
     * 向量的L2范数（模长）
     * 预计算存储，用于优化余弦相似度计算
     * 余弦相似度 = dot(a,b) / (norm(a) * norm(b))
     */
    private Float vectorNorm;

    /**
     * 特征提取模型名称
     * 如：clip-vit-base-patch32、resnet50、vgg16等
     */
    private String extractionModel;

    /**
     * 模型版本号
     * 用于追踪模型版本，便于后续升级和兼容性处理
     */
    private String extractionVersion;

    /**
     * 向量质量评分，范围0.0-1.0
     * 用于评估特征向量的质量，可用于搜索结果的质量控制
     */
    private Float qualityScore;

    /**
     * 记录创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 记录更新时间
     */
    private LocalDateTime updatedTime;
}
