package cn.iflytek.imagesearch.infrastructure.persistence.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 图片数据持久化对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImagePO {

    /**
     * 图片唯一标识符
     */
    private String id;

    /**
     * 图片访问URL地址
     */
    private String url;

    /**
     * 图片宽度，单位像素
     */
    private Integer width;

    /**
     * 图片高度，单位像素
     */
    private Integer height;

    /**
     * 文件大小，单位字节
     */
    private Long fileSize;

    /**
     * 图片格式，如jpg、png、gif等
     */
    private String format;

    /**
     * 图片上传时间
     */
    private LocalDateTime uploadTime;

    /**
     * 图片主色调，十六进制颜色值，如#FF0000
     */
    private String dominantColor;

    /**
     * 图片特征向量，JSON格式存储
     */
    private String featureVector;

    /**
     * 语义相似度得分，范围0.0-1.0
     */
    private Float semanticScore;

    /**
     * 元数据匹配得分，范围0.0-1.0
     */
    private Float metadataScore;

    /**
     * 综合得分，结合语义和元数据得分的加权平均值
     */
    private Float combinedScore;

    /**
     * 记录创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 记录更新时间
     */
    private LocalDateTime updatedTime;
}
