package cn.iflytek.imagesearch.infrastructure.persistence.converter;

import cn.iflytek.imagesearch.domain.model.entry.ImageEntity;
import cn.iflytek.imagesearch.domain.model.entry.ImageMetadata;
import cn.iflytek.imagesearch.infrastructure.persistence.po.ImagePO;
import cn.iflytek.imagesearch.infrastructure.persistence.po.ImageTagPO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 图片实体与PO对象转换工具类
 */
public class ImageConverter {

    /**
     * 将ImageEntity转换为ImagePO
     *
     * @param entity 图片实体
     * @return 图片PO对象
     */
    public static ImagePO toImagePO(ImageEntity entity) {
        if (entity == null) {
            return null;
        }

        ImagePO.ImagePOBuilder builder = ImagePO.builder()
                .id(entity.getId())
                .url(entity.getUrl())
                .semanticScore(entity.getSemanticScore())
                .metadataScore(entity.getMetadataScore())
                .combinedScore(entity.getCombinedScore());

        // 处理特征向量
        if (entity.getFeatureVector() != null) {
            builder.featureVector(JSON.toJSONString(entity.getFeatureVector()));
        }

        // 处理元数据
        if (entity.getMetadata() != null) {
            ImageMetadata metadata = entity.getMetadata();
            builder.width(metadata.getWidth())
                    .height(metadata.getHeight())
                    .fileSize(metadata.getFileSize())
                    .format(metadata.getFormat())
                    .uploadTime(metadata.getUploadTime())
                    .dominantColor(metadata.getDominantColor());
        }

        return builder.build();
    }

    /**
     * 将ImagePO和标签列表转换为ImageEntity
     *
     * @param imagePO 图片PO对象
     * @param tagPOList 标签PO对象列表
     * @return 图片实体
     */
    public static ImageEntity toImageEntity(ImagePO imagePO, List<ImageTagPO> tagPOList) {
        if (imagePO == null) {
            return null;
        }

        ImageEntity.ImageEntityBuilder builder = ImageEntity.builder()
                .id(imagePO.getId())
                .url(imagePO.getUrl())
                .semanticScore(imagePO.getSemanticScore())
                .metadataScore(imagePO.getMetadataScore())
                .combinedScore(imagePO.getCombinedScore());

        // 处理特征向量
        if (imagePO.getFeatureVector() != null) {
            try {
                float[] featureVector = JSON.parseObject(imagePO.getFeatureVector(), float[].class);
                builder.featureVector(featureVector);
            } catch (Exception e) {
                // 忽略解析错误，保持为null
            }
        }

        // 构建元数据
        List<String> tags = tagPOList != null ? 
                tagPOList.stream().map(ImageTagPO::getTag).collect(Collectors.toList()) : 
                new ArrayList<>();

        ImageMetadata metadata = ImageMetadata.builder()
                .width(imagePO.getWidth())
                .height(imagePO.getHeight())
                .fileSize(imagePO.getFileSize())
                .format(imagePO.getFormat())
                .uploadTime(imagePO.getUploadTime())
                .dominantColor(imagePO.getDominantColor())
                .tags(tags)
                .build();

        builder.metadata(metadata);

        return builder.build();
    }

    /**
     * 将标签列表转换为ImageTagPO列表
     *
     * @param imageId 图片ID
     * @param tags 标签列表
     * @return ImageTagPO列表
     */
    public static List<ImageTagPO> toImageTagPOList(String imageId, List<String> tags) {
        if (tags == null || tags.isEmpty()) {
            return new ArrayList<>();
        }

        return tags.stream()
                .map(tag -> ImageTagPO.builder()
                        .imageId(imageId)
                        .tag(tag)
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 批量转换ImagePO和标签映射为ImageEntity列表
     *
     * @param imagePOList 图片PO对象列表
     * @param tagMap 标签映射，key为图片ID，value为标签PO列表
     * @return 图片实体列表
     */
    public static List<ImageEntity> toImageEntityList(List<ImagePO> imagePOList, Map<String, List<ImageTagPO>> tagMap) {
        if (imagePOList == null || imagePOList.isEmpty()) {
            return new ArrayList<>();
        }

        return imagePOList.stream()
                .map(imagePO -> {
                    List<ImageTagPO> tagPOList = tagMap != null ? tagMap.get(imagePO.getId()) : null;
                    return toImageEntity(imagePO, tagPOList);
                })
                .collect(Collectors.toList());
    }
}
