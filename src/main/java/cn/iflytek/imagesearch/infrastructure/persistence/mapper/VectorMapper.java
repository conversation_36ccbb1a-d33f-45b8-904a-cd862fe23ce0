package cn.iflytek.imagesearch.infrastructure.persistence.mapper;

import cn.iflytek.imagesearch.infrastructure.persistence.po.VectorPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图片特征向量数据访问Mapper接口
 * 
 * 提供图片特征向量的基本CRUD操作和向量搜索相关的数据访问功能。
 * 支持多种向量类型的存储和查询，为向量搜索服务提供数据支持。
 */
@Mapper
public interface VectorMapper {

    /**
     * 根据主键ID查询向量记录
     *
     * @param id 主键ID
     * @return 向量PO对象，不存在时返回null
     */
    VectorPO selectById(@Param("id") Long id);

    /**
     * 根据图片ID和向量类型查询向量记录
     *
     * @param imageId 图片ID
     * @param vectorType 向量类型
     * @return 向量PO对象，不存在时返回null
     */
    VectorPO selectByImageIdAndType(@Param("imageId") String imageId, @Param("vectorType") String vectorType);

    /**
     * 根据图片ID查询所有向量记录
     *
     * @param imageId 图片ID
     * @return 向量PO对象列表
     */
    List<VectorPO> selectByImageId(@Param("imageId") String imageId);

    /**
     * 根据图片ID列表批量查询向量记录
     *
     * @param imageIds 图片ID列表
     * @param vectorType 向量类型，可选参数，为null时查询所有类型
     * @return 向量PO对象列表
     */
    List<VectorPO> selectByImageIds(@Param("imageIds") List<String> imageIds, 
                                   @Param("vectorType") String vectorType);

    /**
     * 根据向量类型查询所有向量记录
     * 用于全库向量搜索
     *
     * @param vectorType 向量类型
     * @param limit 限制数量，0表示不限制
     * @return 向量PO对象列表
     */
    List<VectorPO> selectByVectorType(@Param("vectorType") String vectorType, @Param("limit") int limit);

    /**
     * 根据向量类型和候选图片ID列表查询向量记录
     * 用于候选集内的向量搜索
     *
     * @param vectorType 向量类型
     * @param candidateIds 候选图片ID列表
     * @return 向量PO对象列表
     */
    List<VectorPO> selectByVectorTypeAndCandidates(@Param("vectorType") String vectorType, 
                                                  @Param("candidateIds") List<String> candidateIds);

    /**
     * 插入向量记录
     *
     * @param vectorPO 向量PO对象
     * @return 影响行数
     */
    int insert(VectorPO vectorPO);

    /**
     * 批量插入向量记录
     *
     * @param vectorList 向量PO对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("vectorList") List<VectorPO> vectorList);

    /**
     * 更新向量记录
     *
     * @param vectorPO 向量PO对象
     * @return 影响行数
     */
    int update(VectorPO vectorPO);

    /**
     * 根据主键ID删除向量记录
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据图片ID删除所有向量记录
     *
     * @param imageId 图片ID
     * @return 影响行数
     */
    int deleteByImageId(@Param("imageId") String imageId);

    /**
     * 根据图片ID和向量类型删除向量记录
     *
     * @param imageId 图片ID
     * @param vectorType 向量类型
     * @return 影响行数
     */
    int deleteByImageIdAndType(@Param("imageId") String imageId, @Param("vectorType") String vectorType);

    /**
     * 根据图片ID列表批量删除向量记录
     *
     * @param imageIds 图片ID列表
     * @return 影响行数
     */
    int deleteByImageIds(@Param("imageIds") List<String> imageIds);

    /**
     * 检查向量记录是否存在
     *
     * @param imageId 图片ID
     * @param vectorType 向量类型
     * @return 存在返回1，不存在返回0
     */
    int existsByImageIdAndType(@Param("imageId") String imageId, @Param("vectorType") String vectorType);

    /**
     * 获取指定向量类型的记录总数
     *
     * @param vectorType 向量类型，为null时统计所有类型
     * @return 记录总数
     */
    long countByVectorType(@Param("vectorType") String vectorType);

    /**
     * 获取所有向量类型列表
     *
     * @return 向量类型列表
     */
    List<String> selectDistinctVectorTypes();
}
