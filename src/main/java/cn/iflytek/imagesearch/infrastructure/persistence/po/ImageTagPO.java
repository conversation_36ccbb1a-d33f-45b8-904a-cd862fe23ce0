package cn.iflytek.imagesearch.infrastructure.persistence.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 图片标签数据持久化对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImageTagPO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 图片ID，关联image表
     */
    private String imageId;

    /**
     * 标签名称
     */
    private String tag;

    /**
     * 记录创建时间
     */
    private LocalDateTime createdTime;
}
