package cn.iflytek.imagesearch.infrastructure.persistence.mapper;

import cn.iflytek.imagesearch.infrastructure.persistence.po.ImageTagPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图片标签数据访问Mapper接口
 */
@Mapper
public interface ImageTagMapper {

    /**
     * 根据图片ID查找所有标签
     *
     * @param imageId 图片ID
     * @return 标签PO对象列表
     */
    List<ImageTagPO> selectByImageId(@Param("imageId") String imageId);

    /**
     * 根据图片ID列表批量查找标签
     *
     * @param imageIds 图片ID列表
     * @return 标签PO对象列表
     */
    List<ImageTagPO> selectByImageIds(@Param("imageIds") List<String> imageIds);

    /**
     * 插入标签记录
     *
     * @param imageTagPO 标签PO对象
     * @return 影响行数
     */
    int insert(ImageTagPO imageTagPO);

    /**
     * 批量插入标签记录
     *
     * @param tagList 标签PO对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("tagList") List<ImageTagPO> tagList);

    /**
     * 根据图片ID删除所有标签
     *
     * @param imageId 图片ID
     * @return 影响行数
     */
    int deleteByImageId(@Param("imageId") String imageId);

    /**
     * 根据图片ID列表批量删除标签
     *
     * @param imageIds 图片ID列表
     * @return 影响行数
     */
    int deleteByImageIds(@Param("imageIds") List<String> imageIds);
}
