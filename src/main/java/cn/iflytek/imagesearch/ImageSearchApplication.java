package cn.iflytek.imagesearch;

import cn.iflytek.imagesearch.domain.service.mcp.ImageSearchMcpService;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

@SpringBootApplication
public class ImageSearchApplication {

    public static void main(String[] args) {
        SpringApplication.run(ImageSearchApplication.class, args);
    }

    /**
     * 注册图片搜索MCP工具
     */
    @Bean
    public ToolCallbackProvider imageSearchTools(ImageSearchMcpService imageSearchMcpService) {
        return MethodToolCallbackProvider.builder()
                .toolObjects(imageSearchMcpService)
                .build();
    }

}
