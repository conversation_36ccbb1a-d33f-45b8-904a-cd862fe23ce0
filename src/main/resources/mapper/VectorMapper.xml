<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iflytek.imagesearch.infrastructure.persistence.mapper.VectorMapper">

    <!-- 结果映射 -->
    <resultMap id="VectorPOResultMap" type="cn.iflytek.imagesearch.infrastructure.persistence.po.VectorPO">
        <id column="id" property="id"/>
        <result column="image_id" property="imageId"/>
        <result column="vector_type" property="vectorType"/>
        <result column="vector_dimension" property="vectorDimension"/>
        <result column="feature_vector" property="featureVector"/>
        <result column="vector_norm" property="vectorNorm"/>
        <result column="extraction_model" property="extractionModel"/>
        <result column="extraction_version" property="extractionVersion"/>
        <result column="quality_score" property="qualityScore"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, image_id, vector_type, vector_dimension, feature_vector, vector_norm,
        extraction_model, extraction_version, quality_score, created_time, updated_time
    </sql>

    <!-- 根据主键ID查询 -->
    <select id="selectById" resultMap="VectorPOResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image_vector
        WHERE id = #{id}
    </select>

    <!-- 根据图片ID和向量类型查询 -->
    <select id="selectByImageIdAndType" resultMap="VectorPOResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image_vector
        WHERE image_id = #{imageId} AND vector_type = #{vectorType}
    </select>

    <!-- 根据图片ID查询所有向量 -->
    <select id="selectByImageId" resultMap="VectorPOResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image_vector
        WHERE image_id = #{imageId}
        ORDER BY vector_type
    </select>

    <!-- 根据图片ID列表批量查询 -->
    <select id="selectByImageIds" resultMap="VectorPOResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image_vector
        WHERE image_id IN
        <foreach collection="imageIds" item="imageId" open="(" separator="," close=")">
            #{imageId}
        </foreach>
        <if test="vectorType != null">
            AND vector_type = #{vectorType}
        </if>
        ORDER BY image_id, vector_type
    </select>

    <!-- 根据向量类型查询所有向量 -->
    <select id="selectByVectorType" resultMap="VectorPOResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image_vector
        WHERE vector_type = #{vectorType}
        ORDER BY created_time DESC
        <if test="limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据向量类型和候选ID列表查询 -->
    <select id="selectByVectorTypeAndCandidates" resultMap="VectorPOResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image_vector
        WHERE vector_type = #{vectorType}
        AND image_id IN
        <foreach collection="candidateIds" item="imageId" open="(" separator="," close=")">
            #{imageId}
        </foreach>
        ORDER BY image_id
    </select>

    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO image_vector (
            image_id, vector_type, vector_dimension, feature_vector, vector_norm,
            extraction_model, extraction_version, quality_score
        ) VALUES (
            #{imageId}, #{vectorType}, #{vectorDimension}, #{featureVector}, #{vectorNorm},
            #{extractionModel}, #{extractionVersion}, #{qualityScore}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="insertBatch">
        INSERT INTO image_vector (
            image_id, vector_type, vector_dimension, feature_vector, vector_norm,
            extraction_model, extraction_version, quality_score
        ) VALUES
        <foreach collection="vectorList" item="vector" separator=",">
            (#{vector.imageId}, #{vector.vectorType}, #{vector.vectorDimension}, 
             #{vector.featureVector}, #{vector.vectorNorm}, #{vector.extractionModel}, 
             #{vector.extractionVersion}, #{vector.qualityScore})
        </foreach>
    </insert>

    <!-- 更新记录 -->
    <update id="update">
        UPDATE image_vector
        <set>
            <if test="imageId != null">image_id = #{imageId},</if>
            <if test="vectorType != null">vector_type = #{vectorType},</if>
            <if test="vectorDimension != null">vector_dimension = #{vectorDimension},</if>
            <if test="featureVector != null">feature_vector = #{featureVector},</if>
            <if test="vectorNorm != null">vector_norm = #{vectorNorm},</if>
            <if test="extractionModel != null">extraction_model = #{extractionModel},</if>
            <if test="extractionVersion != null">extraction_version = #{extractionVersion},</if>
            <if test="qualityScore != null">quality_score = #{qualityScore},</if>
            updated_time = CURRENT_TIMESTAMP
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据主键ID删除 -->
    <delete id="deleteById">
        DELETE FROM image_vector WHERE id = #{id}
    </delete>

    <!-- 根据图片ID删除所有向量 -->
    <delete id="deleteByImageId">
        DELETE FROM image_vector WHERE image_id = #{imageId}
    </delete>

    <!-- 根据图片ID和向量类型删除 -->
    <delete id="deleteByImageIdAndType">
        DELETE FROM image_vector 
        WHERE image_id = #{imageId} AND vector_type = #{vectorType}
    </delete>

    <!-- 根据图片ID列表批量删除 -->
    <delete id="deleteByImageIds">
        DELETE FROM image_vector
        WHERE image_id IN
        <foreach collection="imageIds" item="imageId" open="(" separator="," close=")">
            #{imageId}
        </foreach>
    </delete>

    <!-- 检查记录是否存在 -->
    <select id="existsByImageIdAndType" resultType="int">
        SELECT COUNT(1)
        FROM image_vector
        WHERE image_id = #{imageId} AND vector_type = #{vectorType}
    </select>

    <!-- 统计记录数量 -->
    <select id="countByVectorType" resultType="long">
        SELECT COUNT(1)
        FROM image_vector
        <if test="vectorType != null">
            WHERE vector_type = #{vectorType}
        </if>
    </select>

    <!-- 获取所有向量类型 -->
    <select id="selectDistinctVectorTypes" resultType="string">
        SELECT DISTINCT vector_type
        FROM image_vector
        ORDER BY vector_type
    </select>

</mapper>
