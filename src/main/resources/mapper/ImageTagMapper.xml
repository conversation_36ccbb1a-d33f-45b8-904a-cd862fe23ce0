<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iflytek.imagesearch.infrastructure.persistence.mapper.ImageTagMapper">

    <!-- 结果映射 -->
    <resultMap id="ImageTagPOResultMap" type="cn.iflytek.imagesearch.infrastructure.persistence.po.ImageTagPO">
        <id column="id" property="id"/>
        <result column="image_id" property="imageId"/>
        <result column="tag" property="tag"/>
        <result column="created_time" property="createdTime"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, image_id, tag, created_time
    </sql>

    <!-- 根据图片ID查询标签 -->
    <select id="selectByImageId" resultMap="ImageTagPOResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image_tag
        WHERE image_id = #{imageId}
        ORDER BY created_time ASC
    </select>

    <!-- 根据图片ID列表批量查询标签 -->
    <select id="selectByImageIds" resultMap="ImageTagPOResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image_tag
        WHERE image_id IN
        <foreach collection="imageIds" item="imageId" open="(" separator="," close=")">
            #{imageId}
        </foreach>
        ORDER BY image_id, created_time ASC
    </select>

    <!-- 插入标签记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO image_tag (image_id, tag)
        VALUES (#{imageId}, #{tag})
    </insert>

    <!-- 批量插入标签记录 -->
    <insert id="insertBatch">
        INSERT INTO image_tag (image_id, tag) VALUES
        <foreach collection="tagList" item="tag" separator=",">
            (#{tag.imageId}, #{tag.tag})
        </foreach>
    </insert>

    <!-- 根据图片ID删除标签 -->
    <delete id="deleteByImageId">
        DELETE FROM image_tag WHERE image_id = #{imageId}
    </delete>

    <!-- 根据图片ID列表批量删除标签 -->
    <delete id="deleteByImageIds">
        DELETE FROM image_tag
        WHERE image_id IN
        <foreach collection="imageIds" item="imageId" open="(" separator="," close=")">
            #{imageId}
        </foreach>
    </delete>

</mapper>
