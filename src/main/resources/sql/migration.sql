-- 数据迁移脚本
-- 将现有image表中的feature_vector数据迁移到新的image_vector表

-- 迁移现有的特征向量数据到新的向量表
INSERT INTO image_vector (
    image_id, 
    vector_type, 
    vector_dimension, 
    feature_vector, 
    extraction_model, 
    extraction_version,
    quality_score
)
SELECT 
    id as image_id,
    'CLIP' as vector_type,
    512 as vector_dimension,  -- 假设现有向量都是512维
    feature_vector,
    'legacy' as extraction_model,
    '1.0' as extraction_version,
    0.8 as quality_score  -- 给现有数据一个默认质量评分
FROM image 
WHERE feature_vector IS NOT NULL 
  AND feature_vector != ''
  AND NOT EXISTS (
      SELECT 1 FROM image_vector iv 
      WHERE iv.image_id = image.id 
        AND iv.vector_type = 'CLIP'
  );

-- 可选：清理image表中的feature_vector字段（如果确定不再需要）
-- 注意：执行前请确保数据已成功迁移并测试通过
-- ALTER TABLE image DROP COLUMN feature_vector;

-- 更新向量范数（如果需要）
-- 注意：这需要应用程序层面的支持来解析JSON并计算范数
-- UPDATE image_vector SET vector_norm = ... WHERE vector_norm IS NULL;
