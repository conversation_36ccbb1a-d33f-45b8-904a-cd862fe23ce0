-- 图片搜索系统数据库表结构（使用MariaDB Vector支持）
-- 创建图片主表
CREATE TABLE IF NOT EXISTS `image` (
    `id` VARCHAR(64) NOT NULL COMMENT '图片唯一标识符',
    `url` VARCHAR(512) NOT NULL COMMENT '图片访问URL地址',
    `width` INT NOT NULL DEFAULT 0 COMMENT '图片宽度，单位像素',
    `height` INT NOT NULL DEFAULT 0 COMMENT '图片高度，单位像素',
    `file_size` BIGINT NOT NULL DEFAULT 0 COMMENT '文件大小，单位字节',
    `format` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '图片格式，如jpg、png、gif等',
    `upload_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '图片上传时间',
    `dominant_color` VARCHAR(7) DEFAULT NULL COMMENT '图片主色调，十六进制颜色值，如#FF0000',
    `feature_vector` LONGTEXT DEFAULT NULL COMMENT '图片特征向量，JSON格式存储',
    `semantic_score` FLOAT DEFAULT 0.0 COMMENT '语义相似度得分，范围0.0-1.0',
    `metadata_score` FLOAT DEFAULT 0.0 COMMENT '元数据匹配得分，范围0.0-1.0',
    `combined_score` FLOAT DEFAULT 0.0 COMMENT '综合得分，结合语义和元数据得分的加权平均值',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_image_url` (`url`),
    KEY `idx_upload_time` (`upload_time`),
    KEY `idx_format` (`format`),
    KEY `idx_semantic_score` (`semantic_score`),
    KEY `idx_combined_score` (`combined_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片信息表';

-- 创建图片标签表
CREATE TABLE IF NOT EXISTS `image_tag` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `image_id` VARCHAR(64) NOT NULL COMMENT '图片ID，关联image表',
    `tag` VARCHAR(100) NOT NULL COMMENT '标签名称',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_image_tag` (`image_id`, `tag`),
    KEY `idx_tag` (`tag`),
    KEY `idx_image_id` (`image_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片标签表';

-- 创建向量索引表（支持多种向量类型）
CREATE TABLE IF NOT EXISTS `image_vector` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `image_id` VARCHAR(64) NOT NULL COMMENT '图片ID，关联image表',
    `vector_type` VARCHAR(20) NOT NULL DEFAULT 'CLIP' COMMENT '向量类型：CLIP、ResNet、VGG等',
    `vector_dimension` INT NOT NULL DEFAULT 1024 COMMENT '向量维度',
    `feature_vector` VECTOR(1024) NOT NULL COMMENT '特征向量，1024',
    `vector_norm` FLOAT DEFAULT NULL COMMENT '向量的L2范数，用于优化余弦相似度计算',
    `extraction_model` VARCHAR(50) DEFAULT NULL COMMENT '特征提取模型名称',
    `extraction_version` VARCHAR(20) DEFAULT NULL COMMENT '模型版本号',
    `quality_score` FLOAT DEFAULT NULL COMMENT '向量质量评分，范围0.0-1.0',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_image_vector` (`image_id`, `vector_type`),
    KEY `idx_vector_type` (`vector_type`),
    KEY `idx_vector_dimension` (`vector_dimension`),
    KEY `idx_quality_score` (`quality_score`),
    KEY `idx_created_time` (`created_time`),
    KEY `idx_image_id_vector` (`image_id`),
    VECTOR INDEX (`feature_vector`) M=16 DISTANCE=cosine
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片特征向量表';

-- 创建1536维向量表（例如：OpenAI embeddings）
CREATE TABLE IF NOT EXISTS `image_vector_1536` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `image_id` VARCHAR(64) NOT NULL COMMENT '图片ID，关联image表',
    `vector_type` VARCHAR(20) NOT NULL DEFAULT 'OpenAI' COMMENT '向量类型',
    `feature_vector` VECTOR(1536) NOT NULL COMMENT '特征向量，1536维向量',
    `quality_score` FLOAT DEFAULT NULL COMMENT '向量质量评分，范围0.0-1.0',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_image_vector_1536` (`image_id`, `vector_type`),
    KEY `idx_image_id_1536` (`image_id`),
    VECTOR INDEX (`feature_vector`) M=8 DISTANCE=cosine
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片1536维特征向量表';
