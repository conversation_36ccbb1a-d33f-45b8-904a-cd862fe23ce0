# 向量搜索相关配置示例
image-search:
  vector:
    # 默认向量类型
    default-vector-type: CLIP
    
    # 最小相似度阈值
    min-similarity-threshold: 0.1
    
    # 最大搜索结果数量
    max-search-results: 1000
    
    # 向量维度
    vector-dimension: 512
    
    # 嵌入模型配置
    embedding:
      # 是否启用文本嵌入
      enabled: true
      
      # 嵌入模型类型
      model-type: CLIP
      
      # 模型名称
      model-name: clip-vit-base-patch32
      
      # 模型版本
      model-version: "1.0"
      
      # API端点URL（如果使用外部服务）
      api-endpoint: "http://localhost:8080/api/embeddings"
      
      # API密钥
      api-key: "${EMBEDDING_API_KEY:}"
      
      # 请求超时时间（毫秒）
      timeout-ms: 10000
      
      # 批处理大小
      batch-size: 32
    
    # 相似度计算配置
    similarity:
      # 相似度计算方法：cosine、euclidean、dot
      method: cosine
      
      # 是否启用向量归一化
      normalize-vectors: true
      
      # 是否使用预计算的向量范数
      use-precomputed-norm: true
    
    # 缓存配置
    cache:
      # 是否启用查询向量缓存
      enable-query-cache: true
      
      # 查询缓存过期时间（秒）
      query-cache-expire-seconds: 3600
      
      # 是否启用结果缓存
      enable-result-cache: false
      
      # 结果缓存过期时间（秒）
      result-cache-expire-seconds: 300
      
      # 缓存最大条目数
      max-cache-entries: 1000
