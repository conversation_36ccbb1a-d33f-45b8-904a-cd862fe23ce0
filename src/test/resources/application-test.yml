spring:
  # 测试数据库配置 - 使用H2内存数据库
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    hikari:
      maximum-pool-size: 5
      minimum-idle: 1
      connection-timeout: 30000

  # H2数据库控制台（可选，用于调试）
  h2:
    console:
      enabled: true
      path: /h2-console

  # MyBatis配置
  mybatis:
    mapper-locations: classpath:mapper/*.xml
    type-aliases-package: cn.iflytek.imagesearch.infrastructure.persistence.po
    configuration:
      map-underscore-to-camel-case: true
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 日志配置
logging:
  level:
    cn.iflytek.imagesearch: DEBUG
    org.springframework.jdbc: DEBUG
