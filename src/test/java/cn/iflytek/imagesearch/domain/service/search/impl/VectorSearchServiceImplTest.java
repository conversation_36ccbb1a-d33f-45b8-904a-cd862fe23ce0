package cn.iflytek.imagesearch.domain.service.search.impl;

import cn.iflytek.imagesearch.domain.model.entry.ImageEntity;
import cn.iflytek.imagesearch.domain.model.entry.ImageMetadata;
import cn.iflytek.imagesearch.domain.repository.ImageRepository;
import cn.iflytek.imagesearch.domain.repository.VectorRepository;
import cn.iflytek.imagesearch.domain.service.search.VectorSearchService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * VectorSearchService实现类测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class VectorSearchServiceImplTest {

    @Autowired
    private VectorSearchService vectorSearchService;

    @Autowired
    private VectorRepository vectorRepository;

    @Autowired
    private ImageRepository imageRepository;

    @BeforeEach
    public void setUp() {
        // 准备测试数据
        setupTestData();
    }

    @Test
    public void testSemanticSearch() {
        // 执行语义搜索
        List<ImageEntity> results = vectorSearchService.semanticSearch("可爱的小猫", 10);
        
        // 验证结果
        assertNotNull(results);
        assertTrue(results.size() <= 10);
        
        // 验证结果按相似度降序排列
        for (int i = 1; i < results.size(); i++) {
            assertTrue(results.get(i-1).getSemanticScore() >= results.get(i).getSemanticScore());
        }
        
        // 验证每个结果都有相似度得分
        for (ImageEntity entity : results) {
            assertNotNull(entity.getSemanticScore());
            assertTrue(entity.getSemanticScore() >= 0.0f && entity.getSemanticScore() <= 1.0f);
        }
    }

    @Test
    public void testSemanticSearchWithinCandidates() {
        // 准备候选集
        Set<String> candidateIds = new HashSet<>(Arrays.asList("test-image-001", "test-image-002"));
        
        // 执行候选集内语义搜索
        List<ImageEntity> results = vectorSearchService.semanticSearchWithinCandidates(
                "美丽的风景", candidateIds, 5);
        
        // 验证结果
        assertNotNull(results);
        assertTrue(results.size() <= 5);
        assertTrue(results.size() <= candidateIds.size());
        
        // 验证所有结果都在候选集内
        for (ImageEntity entity : results) {
            assertTrue(candidateIds.contains(entity.getId()));
            assertNotNull(entity.getSemanticScore());
        }
    }

    @Test
    public void testSemanticSearchWithEmptyQuery() {
        // 测试空查询
        assertThrows(IllegalArgumentException.class, () -> {
            vectorSearchService.semanticSearch("", 10);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            vectorSearchService.semanticSearch(null, 10);
        });
    }

    @Test
    public void testSemanticSearchWithInvalidLimit() {
        // 测试无效限制数量
        assertThrows(IllegalArgumentException.class, () -> {
            vectorSearchService.semanticSearch("测试查询", 0);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            vectorSearchService.semanticSearch("测试查询", -1);
        });
    }

    @Test
    public void testSemanticSearchWithinCandidatesWithEmptyCandidates() {
        // 测试空候选集
        assertThrows(IllegalArgumentException.class, () -> {
            vectorSearchService.semanticSearchWithinCandidates("测试查询", new HashSet<>(), 10);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            vectorSearchService.semanticSearchWithinCandidates("测试查询", null, 10);
        });
    }

    /**
     * 设置测试数据
     */
    private void setupTestData() {
        // 创建测试图片实体
        ImageEntity image1 = createTestImageEntity("test-image-001", "http://example.com/cat.jpg", 
                                                  Arrays.asList("cat", "cute"));
        ImageEntity image2 = createTestImageEntity("test-image-002", "http://example.com/landscape.jpg", 
                                                  Arrays.asList("landscape", "nature"));
        ImageEntity image3 = createTestImageEntity("test-image-003", "http://example.com/dog.jpg", 
                                                  Arrays.asList("dog", "pet"));

        // 保存图片实体
        imageRepository.save(image1);
        imageRepository.save(image2);
        imageRepository.save(image3);

        // 创建测试向量数据
        VectorRepository.VectorEntity vector1 = createTestVectorEntity("test-image-001", generateRandomVector());
        VectorRepository.VectorEntity vector2 = createTestVectorEntity("test-image-002", generateRandomVector());
        VectorRepository.VectorEntity vector3 = createTestVectorEntity("test-image-003", generateRandomVector());

        // 保存向量数据
        vectorRepository.save(vector1);
        vectorRepository.save(vector2);
        vectorRepository.save(vector3);
    }

    /**
     * 创建测试图片实体
     */
    private ImageEntity createTestImageEntity(String id, String url, List<String> tags) {
        ImageMetadata metadata = ImageMetadata.builder()
                .width(1920)
                .height(1080)
                .fileSize(2048000L)
                .format("jpg")
                .uploadTime(LocalDateTime.now())
                .dominantColor("#FF5733")
                .tags(tags)
                .build();

        return ImageEntity.builder()
                .id(id)
                .url(url)
                .metadata(metadata)
                .build();
    }

    /**
     * 创建测试向量实体
     */
    private VectorRepository.VectorEntity createTestVectorEntity(String imageId, float[] vector) {
        VectorRepository.VectorEntity entity = new VectorRepository.VectorEntity();
        entity.setImageId(imageId);
        entity.setVectorType("CLIP");
        entity.setFeatureVector(vector);
        entity.setExtractionModel("clip-vit-base-patch32");
        entity.setExtractionVersion("1.0");
        entity.setQualityScore(0.9f);
        return entity;
    }

    /**
     * 生成随机向量
     */
    private float[] generateRandomVector() {
        Random random = new Random();
        float[] vector = new float[512];
        for (int i = 0; i < vector.length; i++) {
            vector[i] = (random.nextFloat() - 0.5f) * 2.0f;
        }
        
        // 归一化
        double norm = 0.0;
        for (float value : vector) {
            norm += value * value;
        }
        norm = Math.sqrt(norm);
        
        if (norm > 0) {
            for (int i = 0; i < vector.length; i++) {
                vector[i] = (float) (vector[i] / norm);
            }
        }
        
        return vector;
    }
}
