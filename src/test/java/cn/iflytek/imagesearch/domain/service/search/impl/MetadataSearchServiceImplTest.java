package cn.iflytek.imagesearch.domain.service.search.impl;

import cn.iflytek.imagesearch.domain.model.entry.ImageEntity;
import cn.iflytek.imagesearch.domain.model.entry.ImageMetadata;
import cn.iflytek.imagesearch.domain.model.entry.ImageMetadataFilter;
import cn.iflytek.imagesearch.domain.repository.ImageRepository;
import cn.iflytek.imagesearch.domain.service.search.MetadataSearchService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MetadataSearchService实现类测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class MetadataSearchServiceImplTest {

    @Autowired
    private MetadataSearchService metadataSearchService;

    @Autowired
    private ImageRepository imageRepository;

    @Test
    public void testFilterByMetadata_SizeRange() {
        // 准备测试数据
        ImageEntity largeImage = createTestImageEntity("large-001", 1920, 1080);
        ImageEntity smallImage = createTestImageEntity("small-001", 800, 600);
        
        imageRepository.save(largeImage);
        imageRepository.save(smallImage);

        // 测试尺寸筛选
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .sizeRange(ImageMetadataFilter.SizeRange.builder()
                        .minWidth(1000)
                        .minHeight(800)
                        .build())
                .build();

        Set<String> result = metadataSearchService.filterByMetadata(filter);
        
        assertEquals(1, result.size());
        assertTrue(result.contains("large-001"));
        assertFalse(result.contains("small-001"));
    }

    @Test
    public void testFilterByMetadata_FileSize() {
        // 准备测试数据
        ImageEntity bigFile = createTestImageEntity("big-001", 1920, 1080);
        bigFile.getMetadata().setFileSize(5 * 1024 * 1024L); // 5MB
        
        ImageEntity smallFile = createTestImageEntity("small-001", 800, 600);
        smallFile.getMetadata().setFileSize(1024 * 1024L); // 1MB
        
        imageRepository.save(bigFile);
        imageRepository.save(smallFile);

        // 测试文件大小筛选
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .minFileSize(2 * 1024 * 1024L) // 最小2MB
                .build();

        Set<String> result = metadataSearchService.filterByMetadata(filter);
        
        assertEquals(1, result.size());
        assertTrue(result.contains("big-001"));
        assertFalse(result.contains("small-001"));
    }

    @Test
    public void testFilterByMetadata_Format() {
        // 准备测试数据
        ImageEntity jpgImage = createTestImageEntity("jpg-001", 1920, 1080);
        jpgImage.getMetadata().setFormat("jpg");
        
        ImageEntity pngImage = createTestImageEntity("png-001", 800, 600);
        pngImage.getMetadata().setFormat("png");
        
        imageRepository.save(jpgImage);
        imageRepository.save(pngImage);

        // 测试格式筛选
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .formats(Arrays.asList("jpg", "jpeg"))
                .build();

        Set<String> result = metadataSearchService.filterByMetadata(filter);
        
        assertEquals(1, result.size());
        assertTrue(result.contains("jpg-001"));
        assertFalse(result.contains("png-001"));
    }

    @Test
    public void testFilterByMetadata_Tags() {
        // 准备测试数据
        ImageEntity natureImage = createTestImageEntity("nature-001", 1920, 1080);
        natureImage.getMetadata().setTags(Arrays.asList("nature", "landscape", "outdoor"));
        
        ImageEntity cityImage = createTestImageEntity("city-001", 800, 600);
        cityImage.getMetadata().setTags(Arrays.asList("city", "urban", "building"));
        
        imageRepository.save(natureImage);
        imageRepository.save(cityImage);

        // 测试标签筛选
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .tags(Arrays.asList("nature", "landscape"))
                .build();

        Set<String> result = metadataSearchService.filterByMetadata(filter);
        
        assertEquals(1, result.size());
        assertTrue(result.contains("nature-001"));
        assertFalse(result.contains("city-001"));
    }

    @Test
    public void testFilterByMetadata_DateRange() {
        // 准备测试数据
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime yesterday = now.minusDays(1);
        LocalDateTime lastWeek = now.minusDays(7);
        
        ImageEntity recentImage = createTestImageEntity("recent-001", 1920, 1080);
        recentImage.getMetadata().setUploadTime(yesterday);
        
        ImageEntity oldImage = createTestImageEntity("old-001", 800, 600);
        oldImage.getMetadata().setUploadTime(lastWeek);
        
        imageRepository.save(recentImage);
        imageRepository.save(oldImage);

        // 测试日期范围筛选
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .dateRange(ImageMetadataFilter.DateRange.builder()
                        .startDate(now.minusDays(3))
                        .endDate(now)
                        .build())
                .build();

        Set<String> result = metadataSearchService.filterByMetadata(filter);
        
        assertEquals(1, result.size());
        assertTrue(result.contains("recent-001"));
        assertFalse(result.contains("old-001"));
    }

    @Test
    public void testFilterByMetadata_AspectRatio() {
        // 准备测试数据
        ImageEntity wideImage = createTestImageEntity("wide-001", 1920, 1080); // 16:9 ≈ 1.78
        ImageEntity squareImage = createTestImageEntity("square-001", 1000, 1000); // 1:1 = 1.0
        
        imageRepository.save(wideImage);
        imageRepository.save(squareImage);

        // 测试宽高比筛选
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .aspectRatio(ImageMetadataFilter.AspectRatioRange.builder()
                        .minRatio(1.5)
                        .maxRatio(2.0)
                        .build())
                .build();

        Set<String> result = metadataSearchService.filterByMetadata(filter);
        
        assertEquals(1, result.size());
        assertTrue(result.contains("wide-001"));
        assertFalse(result.contains("square-001"));
    }

    @Test
    public void testBuildSpecification() {
        // 准备测试数据
        ImageEntity testImage = createTestImageEntity("test-001", 1920, 1080);
        testImage.getMetadata().setTags(Arrays.asList("nature", "landscape"));
        
        imageRepository.save(testImage);

        // 测试构建规范
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .tags(Arrays.asList("nature"))
                .build();

        List<ImageEntity> result = metadataSearchService.buildSpecification(filter);
        
        assertEquals(1, result.size());
        assertEquals("test-001", result.get(0).getId());
        assertNotNull(result.get(0).getMetadata());
        assertEquals(1920, result.get(0).getMetadata().getWidth());
    }

    @Test
    public void testFilterByMetadata_EmptyFilter() {
        // 测试空筛选条件
        ImageMetadataFilter emptyFilter = ImageMetadataFilter.builder().build();
        
        Set<String> result = metadataSearchService.filterByMetadata(emptyFilter);
        assertTrue(result.isEmpty());
        
        List<ImageEntity> entities = metadataSearchService.buildSpecification(emptyFilter);
        assertTrue(entities.isEmpty());
    }

    @Test
    public void testFilterByMetadata_NullFilter() {
        // 测试null筛选条件
        Set<String> result = metadataSearchService.filterByMetadata(null);
        assertTrue(result.isEmpty());
        
        List<ImageEntity> entities = metadataSearchService.buildSpecification(null);
        assertTrue(entities.isEmpty());
    }

    @Test
    public void testFilterByMetadata_InvalidParameters() {
        // 测试无效参数
        ImageMetadataFilter invalidFilter = ImageMetadataFilter.builder()
                .sizeRange(ImageMetadataFilter.SizeRange.builder()
                        .minWidth(2000)
                        .maxWidth(1000) // 最小值大于最大值
                        .build())
                .build();

        assertThrows(IllegalArgumentException.class, () -> {
            metadataSearchService.filterByMetadata(invalidFilter);
        });
    }

    @Test
    public void testFilterByMetadata_CombinedConditions() {
        // 准备测试数据
        ImageEntity matchingImage = createTestImageEntity("match-001", 1920, 1080);
        matchingImage.getMetadata().setFormat("jpg");
        matchingImage.getMetadata().setTags(Arrays.asList("nature", "landscape"));
        matchingImage.getMetadata().setFileSize(2 * 1024 * 1024L);
        
        ImageEntity nonMatchingImage = createTestImageEntity("nomatch-001", 800, 600);
        nonMatchingImage.getMetadata().setFormat("png");
        nonMatchingImage.getMetadata().setTags(Arrays.asList("city"));
        nonMatchingImage.getMetadata().setFileSize(500 * 1024L);
        
        imageRepository.save(matchingImage);
        imageRepository.save(nonMatchingImage);

        // 测试组合条件筛选
        ImageMetadataFilter filter = ImageMetadataFilter.builder()
                .sizeRange(ImageMetadataFilter.SizeRange.builder()
                        .minWidth(1000)
                        .build())
                .formats(Arrays.asList("jpg"))
                .tags(Arrays.asList("nature"))
                .minFileSize(1024 * 1024L)
                .build();

        Set<String> result = metadataSearchService.filterByMetadata(filter);
        
        assertEquals(1, result.size());
        assertTrue(result.contains("match-001"));
        assertFalse(result.contains("nomatch-001"));
    }

    /**
     * 创建测试用的ImageEntity
     */
    private ImageEntity createTestImageEntity(String id, int width, int height) {
        ImageMetadata metadata = ImageMetadata.builder()
                .width(width)
                .height(height)
                .fileSize(1024000L)
                .format("jpg")
                .uploadTime(LocalDateTime.now())
                .dominantColor("#FF5733")
                .tags(Arrays.asList("test"))
                .build();

        return ImageEntity.builder()
                .id(id)
                .url("http://example.com/" + id + ".jpg")
                .metadata(metadata)
                .featureVector(new float[]{0.1f, 0.2f, 0.3f})
                .semanticScore(0.85f)
                .metadataScore(0.75f)
                .combinedScore(0.80f)
                .build();
    }
}
