package cn.iflytek.imagesearch.infrastructure.repository;

import cn.iflytek.imagesearch.domain.model.entry.ImageEntity;
import cn.iflytek.imagesearch.domain.model.entry.ImageMetadata;
import cn.iflytek.imagesearch.domain.repository.ImageRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ImageRepository实现类测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ImageRepositoryImplTest {

    @Autowired
    private ImageRepository imageRepository;

    @Test
    public void testSaveAndFindById() {
        // 准备测试数据
        ImageEntity imageEntity = createTestImageEntity();

        // 保存图片
        ImageEntity savedEntity = imageRepository.save(imageEntity);
        assertNotNull(savedEntity);
        assertEquals("test-image-001", savedEntity.getId());

        // 根据ID查找
        Optional<ImageEntity> foundEntity = imageRepository.findById("test-image-001");
        assertTrue(foundEntity.isPresent());
        assertEquals("test-image-001", foundEntity.get().getId());
        assertEquals("http://example.com/test-image.jpg", foundEntity.get().getUrl());
        
        // 验证元数据
        ImageMetadata metadata = foundEntity.get().getMetadata();
        assertNotNull(metadata);
        assertEquals(1920, metadata.getWidth());
        assertEquals(1080, metadata.getHeight());
        assertEquals("jpg", metadata.getFormat());
        assertEquals(2, metadata.getTags().size());
        assertTrue(metadata.getTags().contains("nature"));
        assertTrue(metadata.getTags().contains("landscape"));
    }

    @Test
    public void testFindByUrl() {
        // 准备测试数据
        ImageEntity imageEntity = createTestImageEntity();
        imageRepository.save(imageEntity);

        // 根据URL查找
        Optional<ImageEntity> foundEntity = imageRepository.findByUrl("http://example.com/test-image.jpg");
        assertTrue(foundEntity.isPresent());
        assertEquals("test-image-001", foundEntity.get().getId());
    }

    @Test
    public void testFindByIds() {
        // 准备测试数据
        ImageEntity entity1 = createTestImageEntity();
        ImageEntity entity2 = createTestImageEntity();
        entity2.setId("test-image-002");
        entity2.setUrl("http://example.com/test-image-2.jpg");

        imageRepository.save(entity1);
        imageRepository.save(entity2);

        // 批量查找
        List<String> ids = Arrays.asList("test-image-001", "test-image-002");
        List<ImageEntity> foundEntities = imageRepository.findByIds(ids);
        
        assertEquals(2, foundEntities.size());
    }

    @Test
    public void testDeleteById() {
        // 准备测试数据
        ImageEntity imageEntity = createTestImageEntity();
        imageRepository.save(imageEntity);

        // 验证存在
        assertTrue(imageRepository.existsById("test-image-001"));

        // 删除
        boolean deleted = imageRepository.deleteById("test-image-001");
        assertTrue(deleted);

        // 验证已删除
        assertFalse(imageRepository.existsById("test-image-001"));
    }

    @Test
    public void testCount() {
        // 初始数量
        long initialCount = imageRepository.count();

        // 添加测试数据
        ImageEntity imageEntity = createTestImageEntity();
        imageRepository.save(imageEntity);

        // 验证数量增加
        long newCount = imageRepository.count();
        assertEquals(initialCount + 1, newCount);
    }

    @Test
    public void testFindAll() {
        // 准备测试数据
        ImageEntity entity1 = createTestImageEntity();
        ImageEntity entity2 = createTestImageEntity();
        entity2.setId("test-image-002");
        entity2.setUrl("http://example.com/test-image-2.jpg");

        imageRepository.save(entity1);
        imageRepository.save(entity2);

        // 分页查询
        List<ImageEntity> entities = imageRepository.findAll(0, 10);
        assertTrue(entities.size() >= 2);
    }

    /**
     * 创建测试用的ImageEntity
     */
    private ImageEntity createTestImageEntity() {
        ImageMetadata metadata = ImageMetadata.builder()
                .width(1920)
                .height(1080)
                .fileSize(2048000L)
                .format("jpg")
                .uploadTime(LocalDateTime.now())
                .dominantColor("#FF5733")
                .tags(Arrays.asList("nature", "landscape"))
                .build();

        return ImageEntity.builder()
                .id("test-image-001")
                .url("http://example.com/test-image.jpg")
                .metadata(metadata)
                .featureVector(new float[]{0.1f, 0.2f, 0.3f, 0.4f, 0.5f})
                .semanticScore(0.85f)
                .metadataScore(0.75f)
                .combinedScore(0.80f)
                .build();
    }
}
